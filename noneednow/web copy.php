<?php

use Inertia\Inertia;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Application;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DeviceController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EarthquakeController;
use App\Http\Controllers\PermissionController;

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
});

// Admin Dashboard - Admin ve Super Admin
Route::get('/dashboard', DashboardController::class)->middleware(['auth', 'verified', 'permission:admin.dashboard.view'])->name('dashboard');

Route::middleware('auth')->group(function () {
    // Profile - Admin ve Super Admin
    Route::get('/profile', [ProfileController::class, 'edit'])->middleware(['permission:admin.profile.edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->middleware(['permission:admin.profile.update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->middleware(['permission:admin.profile.delete'])->name('profile.destroy');

    // Device Management - Admin ve Super Admin
    Route::get('device', [DeviceController::class, 'index'])->middleware(['permission:admin.device.index'])->name('device.index');
    Route::get('device/create', [DeviceController::class, 'create'])->middleware(['permission:admin.device.create'])->name('device.create');
    Route::post('device', [DeviceController::class, 'store'])->middleware(['permission:admin.device.create'])->name('device.store');
    Route::get('device/{device}', [DeviceController::class, 'show'])->middleware(['permission:admin.device.show'])->name('device.show');
    Route::get('device/{device}/edit', [DeviceController::class, 'edit'])->middleware(['permission:admin.device.edit'])->name('device.edit');
    Route::patch('device/{device}', [DeviceController::class, 'update'])->middleware(['permission:admin.device.edit'])->name('device.update');
    Route::put('device/{device}', [DeviceController::class, 'update'])->middleware(['permission:admin.device.edit'])->name('device.update');
    Route::delete('device/{device}', [DeviceController::class, 'destroy'])->middleware(['permission:admin.device.delete'])->name('device.destroy');

    // Device Operations - Admin ve Super Admin
    Route::post('device/{device}/{operation}', [DeviceController::class, 'job'])->middleware(['permission:admin.device.operations'])->name('device.job');
    Route::post('/job-status', [DeviceController::class, 'getJobStatus'])->middleware(['permission:admin.device.operations'])->name('job-status');
    Route::post('/clear-job-result', [DeviceController::class, 'clearJobResult'])->middleware(['permission:admin.device.operations'])->name('clear-job-result');

    // Device Console - Admin ve Super Admin
    Route::post('/device/{device}/raspberry-pi/console', [DeviceController::class, 'console'])->middleware(['permission:admin.device.operations'])->name('device.raspberry-pi.console');

    // Real-time Device Monitoring - Admin ve Super Admin
    Route::post('/device/{device}/start-monitoring', [DeviceController::class, 'startRealtimeMonitoring'])->middleware(['permission:admin.device.operations'])->name('device.start-monitoring');
    Route::post('/device/{device}/stop-monitoring', [DeviceController::class, 'stopRealtimeMonitoring'])->middleware(['permission:admin.device.operations'])->name('device.stop-monitoring');

    // Admin Earthquake Management - Admin ve Super Admin
    Route::get('earthquakes', [EarthquakeController::class, 'index'])->middleware(['permission:admin.earthquake.index'])->name('earthquakes.index');
    Route::get('earthquakes/{earthquake}', [EarthquakeController::class, 'show'])->middleware(['permission:admin.earthquake.show'])->name('earthquakes.show');
    Route::get('/earthquake-stats', [EarthquakeController::class, 'stats'])->middleware(['permission:admin.earthquake.stats'])->name('earthquake.stats');
    Route::get('/device-stats/{deviceId}', [EarthquakeController::class, 'deviceStats'])->middleware(['permission:admin.earthquake.stats'])->name('earthquake.device-stats');

    // User Management - Sadece Super Admin
    Route::resource('users', UserController::class)->middleware(['permission:super.user.manage']);

    // Role Management - Sadece Super Admin
    Route::resource('roles', RoleController::class)->middleware(['permission:super.role.manage']);

    // Permission Management - Sadece Super Admin
    Route::resource('permissions', PermissionController::class)->middleware(['permission:super.permission.manage']);

    // Telescope - Sadece Super Admin
    Route::get('/telescope', function () {
        return redirect('/telescope');
    })->middleware(['permission:super.telescope.access'])->name('telescope');
});


require __DIR__ . '/auth.php';
require __DIR__ . '/customer.php';
