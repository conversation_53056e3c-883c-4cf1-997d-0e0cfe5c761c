services:
  consumer:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8888:8888"
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - KAFKA_BROKERS=kafka.niny.tech:9093
      - <PERSON>AFKA_SECURITY_PROTOCOL=SASL_PLAINTEXT
      - KAFKA_SASL_MECHANISM=SCRAM-SHA-512
      - KAFKA_SASL_USERNAME=user
      - KAFKA_SASL_PASSWORD=Fatih123
      - KAFKA_TOPICS=pi_001,pi_007,pi_008,pi_009,pi_010,pi_011,balikesir_pi_001 # Yeni <PERSON>az geldiginde buraya ekle
      - WS_PORT=8888
      - HTTP_PORT=3001
      - TOKEN=31mif5cvZUp/RPNTIOlAz+/wqvjg6spsSIfnsoAj7DI=
    volumes:
      - .:/app
    command: ["node", "src/index.js"]
