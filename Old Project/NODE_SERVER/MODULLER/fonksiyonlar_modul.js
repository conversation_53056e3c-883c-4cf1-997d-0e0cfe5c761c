var { exec } = require("child_process");


module.exports = {
    SISTEM_RESET_FONK: function (LOCAL_SOKET_PORT) {
        exec('killall chromium-browser');
        exec('killall python3');
        exec('rm -rf /home/<USER>/Desktop/REVIZYON/PYTHON_CLIENT/__pycache__');
        exec('export DISPLAY=:0;lxterminal --geometry=50x20-0+0 --command="/usr/bin/python3 /home/<USER>/Desktop/REVIZYON/PYTHON_CLIENT/SOKET_MAIN.py"');
        exec(`export DISPLAY=:0;chromium-browser --disable-gpu --kiosk http://localhost:${LOCAL_SOKET_PORT}/`);
        // console.log("SISTEM_RESET_FONK");
    },

    INTERNET_KONTROL: function () {
        exec(`ping -c 1 ${REMOTE_SERVER_IP}`, function (error, stdout, stderr) {
            if (error !== null) {
                console.log("İnternet Kesildi");
                console.log(moment().format("HH:mm:ss DD/MM/YYYY"));
            }
        });
        // setInterval(INTERNET_KONTROL,10000);
    }

}