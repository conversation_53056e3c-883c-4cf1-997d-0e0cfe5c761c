# 🌍 Gelişmiş Deprem İzleme Sistemi - Dashboard Kurulum Rehberi

## 📋 Genel Bakış

Bu yeni dashboard, eski sistem yerine gelişmiş deprem izleme sisteminden gelen verileri görüntüler. Eski sistemin basit ivme gösteriminin aksine, bu dashboard şunları sunar:

### 🆚 Eski Sistem vs Yeni Sistem

| Özellik | Eski Sistem | Yeni Sistem |
|---------|-------------|-------------|
| **Veri Kaynağı** | Socket.IO + Python Client | WebSocket + Raspberry Pi ADXL355 |
| **Analiz** | Basit ivme değerleri | Richter, MMI, PGA hesaplamaları |
| **Alarm Sistemi** | Basit eşik kontrolü | Çok seviyeli alarm (Normal/Seismic/Earthquake) |
| **Kalibrasyon** | Manuel | Otomatik kalibrasyon sistemi |
| **Veri Formatı** | Basit JSON | Kapsamlı analiz verileri |
| **Görselleştirme** | Temel grafik | Modern, responsive dashboard |

## 🔧 Kurulum Adımları

### 1. Sistem Gereksinimleri

- **WebSocket Server**: `earthquake-websocket` projesi çalışır durumda olmalı
- **Raspberry Pi Client**: `rasberrypi-python-client` aktif olmalı
- **Modern Web Tarayıcısı**: Chrome, Firefox, Safari, Edge

### 2. WebSocket Server Konfigürasyonu

WebSocket server'ın çalıştığından emin olun:

```bash
cd earthquake-websocket
npm install
npm start
```

Server şu portlarda çalışacak:
- **WebSocket**: `ws://localhost:8888`
- **HTTP API**: `http://localhost:3001`

### 3. Dashboard Konfigürasyonu

`new-earthquake-dashboard.html` dosyasında aşağıdaki ayarları yapın:

```javascript
// WebSocket bağlantı ayarları
const wsUrl = 'ws://localhost:8888'; // WebSocket server URL'i
const token = 'your-jwt-token-here'; // JWT token (Laravel'den alınacak)
```

### 4. JWT Token Alma

Dashboard'a erişim için JWT token gereklidir. Token'ı Laravel uygulamasından alabilirsiniz:

```php
// Laravel'de token oluşturma
$payload = [
    'id' => $user->id, 
    'iat' => now()->timestamp, 
    'exp' => now()->addHours(1)->timestamp
];
$token = JWT::encode($payload, config('jwt.jwt_secret'), 'HS256');
```

## 📊 Veri Yapısı

### Gelen Veri Formatı

Dashboard şu formatta veri bekler:

```json
{
  "type": "new_earthquake",
  "payload": {
    "device_id": "pi_001",
    "current_acceleration": {
      "x": 0.123,
      "y": -0.045,
      "z": 9.876
    },
    "analysis": {
      "magnitude": 0.234,
      "mmi_scale": 2.1,
      "richter_magnitude": 1.8,
      "pga": {
        "x_pga": 0.001,
        "y_pga": 0.002,
        "z_pga": 0.003,
        "r_pga": 0.004
      },
      "x_filter_percentage": 85.5,
      "y_filter_percentage": 87.2,
      "z_filter_percentage": 89.1,
      "avg_filter_percentage": 87.3
    },
    "alarms": {
      "earthquake_alarm": false,
      "seismic_alarm": true
    },
    "calibration": {
      "x_offset": 0.001,
      "y_offset": -0.002,
      "z_offset": 0.000
    },
    "sample_count": 1234,
    "timestamp": 1705311045
  }
}
```

## 🎨 Dashboard Özellikleri

### Sol Panel - Cihaz Durumu
- **Cihaz Bilgileri**: ID, örnek sayısı, zaman damgası
- **Alarm Durumu**: Görsel alarm göstergesi (Normal/Seismic/Earthquake)
- **İvme Verileri**: X, Y, Z eksenleri için gerçek zamanlı değerler
- **Kalibrasyon**: Otomatik kalibrasyon offset değerleri

### Orta Panel - Grafikler ve Metrikler
- **Gerçek Zamanlı Grafik**: 3 eksen için ivme grafiği
- **Sismik Metrikler**: Büyüklük, MMI, Richter, PGA değerleri
- **Responsive Tasarım**: Farklı ekran boyutlarına uyum

### Sağ Panel - Detaylı Analiz
- **PGA Değerleri**: X, Y, Z, R eksenleri için PGA
- **Filtre Yüzdeleri**: Sinyal kalitesi göstergeleri
- **Sistem Logları**: Gerçek zamanlı sistem mesajları

## 🚨 Alarm Sistemi

### Alarm Seviyeleri

1. **NORMAL** (Yeşil)
   - MMI < 4.0
   - Richter < 1.0
   - Normal operasyon

2. **SİSMİK AKTİVİTE** (Turuncu, yanıp söner)
   - MMI ≥ 4.0 ve < 6.0
   - Richter ≥ 1.0 ve < 5.0
   - Sismik aktivite tespit edildi

3. **DEPREM ALARMI** (Kırmızı, titreşim efekti)
   - MMI ≥ 6.0
   - Richter ≥ 5.0
   - Kritik deprem durumu

## 🔧 Özelleştirme

### Renk Teması Değiştirme

CSS değişkenlerini düzenleyerek renk temasını özelleştirebilirsiniz:

```css
:root {
  --primary-color: #4CAF50;
  --warning-color: #FF9800;
  --danger-color: #f44336;
  --background-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}
```

### Grafik Ayarları

Plotly grafik ayarlarını değiştirmek için:

```javascript
const layout = {
  // Grafik başlığı, eksen etiketleri, renkler vb.
  title: 'Özel Başlık',
  xaxis: { title: 'Zaman' },
  yaxis: { title: 'İvme (g)' }
};
```

### Veri Saklama Süresi

Grafikteki maksimum veri noktası sayısını değiştirmek için:

```javascript
const maxDataPoints = 200; // Varsayılan: 100
```

## 🐛 Sorun Giderme

### Bağlantı Sorunları

1. **WebSocket Bağlantısı Kurulamıyor**
   - WebSocket server'ın çalıştığını kontrol edin
   - Port 8888'in açık olduğunu doğrulayın
   - JWT token'ın geçerli olduğunu kontrol edin

2. **Veri Gelmiyor**
   - Raspberry Pi client'ın çalıştığını kontrol edin
   - Kafka bağlantısını doğrulayın
   - WebSocket server loglarını inceleyin

3. **Grafik Görünmüyor**
   - Plotly kütüphanesinin yüklendiğini kontrol edin
   - Tarayıcı konsolunda hata mesajlarını inceleyin

### Test Modu

Gerçek veri yoksa dashboard test moduna geçer:

```javascript
// Test verisi otomatik olarak başlar
function startTestData() {
  // Simüle edilmiş deprem verileri
}
```

## 📱 Mobil Uyumluluk

Dashboard responsive tasarıma sahiptir:
- **Desktop**: 3 sütunlu layout
- **Tablet**: 2 sütunlu layout
- **Mobil**: Tek sütunlu layout

## 🔒 Güvenlik

- **JWT Authentication**: Tüm WebSocket bağlantıları token ile korunur
- **CORS**: Cross-origin istekleri kontrol edilir
- **Rate Limiting**: Aşırı istek koruması

## 📈 Performans

- **Veri Sıkıştırma**: JSON verileri sıkıştırılır
- **Bellek Yönetimi**: Eski veriler otomatik temizlenir
- **Lazy Loading**: Büyük veri setleri kademeli yüklenir

## 🔄 Güncelleme

Dashboard otomatik güncelleme özelliğine sahiptir:
- **Gerçek Zamanlı**: WebSocket üzerinden anlık veri
- **Otomatik Yeniden Bağlantı**: Bağlantı koptuğunda otomatik yeniden bağlanır
- **Graceful Degradation**: Bağlantı yoksa test verisi kullanır

## 📞 Destek

Sorunlar için:
1. WebSocket server loglarını kontrol edin
2. Tarayıcı geliştirici araçlarını kullanın
3. Sistem loglarını inceleyin
4. Test modunu deneyin
