<?php

namespace App\Services\Panel;

use App\DTO\DeviceDTO;
use App\Models\Device;
use App\Jobs\RaspberryPiOperationJob;
use App\Services\RasberrypiService;
use Illuminate\Support\Facades\Queue;
use Illuminate\Pagination\LengthAwarePaginator;

class DeviceService
{
    public function list(int $page, int $perPage, ?string $search): LengthAwarePaginator
    {
        $query = Device::query();
        if ($search) $query->where('name', 'like', "%{$search}%");

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    public function create(DeviceDTO $deviceDTO): Device
    {
        return Device::create($deviceDTO->toArray());
    }

    public function update(Device $device, DeviceDTO $deviceDTO): bool
    {
        return $device->update($deviceDTO->toArray());
    }

    public function delete(Device $device): void
    {
        Device::findOrFail($device->id)->delete();
    }

    public function get(Device $device): Device
    {
        return Device::findOrFail($device->id);
    }

    public function job(Device $device, string $operation, array $params = []): array
    {
        try {
            $job = new RaspberryPiOperationJob($device, $operation, $params);
            Queue::push($job);

            return [
                'success' => true,
                'job_id' => $job->getJobId(),
                'message' => 'İşlem kuyruğa eklendi'
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function getJobStatus(string $jobId): ?array
    {
        return RaspberryPiOperationJob::getJobStatus($jobId);
    }

    public function devices(): array
    {
        return Device::all()->map(
            fn(Device $device) => [
                'id' => $device->id,
                'name' => $device->name,
                'location' => $device?->location ?? 'Bilinmiyor',
                'status' => $device->is_active ? 'active' : 'inactive',
                'lastSeen' => $device->updated_at ? $device->updated_at->format('Y-m-d H:i:s') : null,
                'battery' => $device?->battery ?? 0,
                'signal' => $device?->signal ?? 'unknown',
                'coordinates' => [
                    'lat' => $device->latitude,
                    'lng' => $device->longitude
                ]
            ]
        )->toArray();
    }

    public function executeConsoleCommand(Device $device, string $command): array
    {
        try {
            $raspberryPiService = new RasberrypiService(
                ip: $device->ip,
                port: $device->port,
                password: $device->password
            );

            // Komut güvenlik kontrolü
            $sanitizedCommand = $this->sanitizeCommand($command);

            $sshCommand = $raspberryPiService->buildConsoleSSHCommand($sanitizedCommand);
            $output = $raspberryPiService->execute($sshCommand);

            return [
                'success' => true,
                'output' => $output,
                'command' => $sanitizedCommand,
                'timestamp' => now()->format('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'output' => null,
                'command' => $command,
                'timestamp' => now()->format('Y-m-d H:i:s')
            ];
        }
    }

    private function sanitizeCommand(string $command): string
    {
        $dangerousCommands = [
            'rm -rf /',
            'rm -rf *',
            'mkfs',
            'dd if=',
            'shutdown',
            'halt',
            'poweroff',
            'init 0',
            'kill -9 -1',
            'killall',
            'pkill',
            ':(){ :|:& };:',
            'chmod 777 /',
            'passwd',
            'su -',
            'sudo su',
        ];

        $commandLower = strtolower(trim($command));

        foreach ($dangerousCommands as $dangerous) {
            if (str_contains($commandLower, strtolower($dangerous))) {
                throw new \Exception("Güvenlik nedeniyle bu komut engellendi: {$dangerous}");
            }
        }

        if (strlen($command) > 1000) {
            throw new \Exception("Komut çok uzun (maksimum 1000 karakter)");
        }

        return $command;
    }
}
