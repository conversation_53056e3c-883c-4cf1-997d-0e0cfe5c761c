<?php

namespace App\Http\Controllers;

use App\DTO\DeviceDTO;
use App\Http\Requests\DeviceRequest;
use App\Jobs\RaspberryPiOperationJob;
use App\Models\Device;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Services\Panel\DeviceService;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

class DeviceController extends Controller
{
    public function __construct(private readonly DeviceService $deviceService) {}

    public function index(Request $request): Response
    {
        $devices = $this->deviceService->list($request->page ?? 1, $request->perPage ?? 10, $request->search ?? null);
        return Inertia::render('Devices/Index', [
            'devices' => $devices,
            'deviceCan' => [
                'create' => $request->user()->can('admin.device.create'),
                'update' => $request->user()->can('admin.device.edit'),
                'delete' => $request->user()->can('admin.device.delete'),
                'show' => $request->user()->can('admin.device.show'),
                'job' => $request->user()->can('admin.device.operations'),
            ],
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('Devices/Form', ['device' => new Device()]);
    }

    public function store(DeviceRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $deviceDTO = new DeviceDTO(
            $data['name'],
            $data['ip'],
            $data['port'],
            $data['password'] ?? null,
            $data['longitude'],
            $data['latitude'],
            (bool) ($data['is_active'] ?? true),
            (bool) ($data['is_mcp'] ?? false)
        );
        $this->deviceService->create($deviceDTO);

        return redirect()->route('device.index')->with('success', __('Device created successfully'));
    }

    public function show(Device $device): Response
    {
        return Inertia::render('Devices/Show', ['device' => $device]);
    }

    public function edit(Device $device): Response
    {
        return Inertia::render('Devices/Form', ['device' => $device]);
    }

    public function update(DeviceRequest $request, Device $device): RedirectResponse
    {
        $data = $request->validated();
        $deviceDTO = new DeviceDTO(
            $data['name'],
            $data['ip'],
            $data['port'],
            $data['password'] ?? null,
            $data['longitude'],
            $data['latitude'],
            (bool) ($data['is_active'] ?? $device->is_active),
            (bool) ($data['is_mcp'] ?? false)
        );
        $deviceUpdated = $this->deviceService->update($device, $deviceDTO);
        if (!$deviceUpdated) return redirect()->route('device.edit', $device->id)->with('error', __('Device update failed'));

        return redirect()->route('device.index')->with('success', __('Device updated successfully'));
    }

    public function destroy(Device $device): RedirectResponse
    {
        $this->deviceService->delete($device);

        return redirect()->route('device.index')->with('success', __('Device deleted successfully'));
    }

    public function job(Device $device, string $operation, Request $request): JsonResponse
    {
        $params = match ($operation) {
            'start-service', 'stop-service', 'restart-service' => [
                'deviceId' => $device->id
            ],
            'build-command' => [
                'command' => $request->input('command', 'ls -la'),
            ],
            'show-config' => [],
            'update-config' => [
                'configKey' => $request->input('configKey', ''),
                'configValue' => $request->input('configValue', ''),
            ],
            'calibrate' => [],
            'test-quality' => [],
            'system-info' => [],
            default => []
        };

        $result = $this->deviceService->job($device, $operation, $params);
        if ($result['success']) {
            Cache::put("raspberry_pi_job_result_{$result['job_id']}", $result, 3600);
        }

        return response()->json($result, $result['success'] ? 200 : 500);
    }

    public function getJobStatus(Request $request): JsonResponse
    {
        $request->validate(['job_id' => 'required|string']);
        $jobStatus = $this->deviceService->getJobStatus($request->job_id);

        if (!$jobStatus) {
            $cachedResult = Cache::get("raspberry_pi_job_result_{$request->job_id}");
            if ($cachedResult) {
                return response()->json(['success' => true, 'data' => $cachedResult], 200);
            }
            return response()->json(['success' => false, 'message' => 'Job bulunamadı veya süresi dolmuş', 'data' => null], 200);
        }

        return response()->json(['success' => true, 'data' => $jobStatus]);
    }

    public function clearJobResult(Request $request): JsonResponse
    {
        $request->validate(['job_id' => 'required|string']);
        $jobId = $request->job_id;
        RaspberryPiOperationJob::clearResultCache($jobId);
        return response()->json(['success' => true, 'message' => 'Cache cleared for re-run']);
    }

    public function console(Device $device, Request $request)
    {
        $request->validate([
            'command' => 'required|string|max:1000'
        ]);

        $command = $request->command;
        try {
            $result = $this->deviceService->executeConsoleCommand($device, $command);

            return response()->json([
                'success' => true,
                'result' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
