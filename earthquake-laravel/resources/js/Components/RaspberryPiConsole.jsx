import { useState, useRef, useEffect } from "react";
import {
    XMarkIcon,
    PlayIcon,
    ArrowPathIcon,
    StopIcon,
    CommandLineIcon,
    ClockIcon,
    CheckCircleIcon,
    XCircleIcon,
    SignalIcon,
    CpuChipIcon,
    PowerIcon,
} from "@heroicons/react/24/outline";
import { router } from "@inertiajs/react";

export default function RaspberryPiConsole({ isOpen, onClose, device }) {
    const [command, setCommand] = useState("");
    const [commandHistory, setCommandHistory] = useState([]);
    const [historyIndex, setHistoryIndex] = useState(-1);
    const [isLoading, setIsLoading] = useState(false);
    const [output, setOutput] = useState([]);
    const [configParams, setConfigParams] = useState({
        configKey: "SEISMIC_MMI_THRESHOLD",
        configValue: "5.0",
    });
    const [showConfigModal, setShowConfigModal] = useState(false);
    const inputRef = useRef(null);
    const outputRef = useRef(null);

    // Hazır komutlar (dinamik olarak güncellenecek)
    const getQuickCommands = () => [
        {
            name: "🚀 Cihazı Başlat",
            command: "./start.sh",
            description: "Python client ve sensör servislerini başlatır",
            color: "green",
            icon: PlayIcon,
        },
        {
            name: "🛑 Cihazı Durdur",
            command: "./stop.sh",
            description: "Tüm servisleri güvenli şekilde durdurur",
            color: "red",
            icon: StopIcon,
        },
        {
            name: "🔄 Yeniden Başlat",
            command: "./restart.sh",
            description: "Servisleri yeniden başlatır",
            color: "blue",
            icon: ArrowPathIcon,
        },
        {
            name: "📊 Sistem Durumu",
            command: "systemctl status adxl355-service",
            description: "ADXL355 servis durumunu gösterir",
            color: "cyan",
            icon: SignalIcon,
        },
        {
            name: "🖥️ Sistem Bilgisi",
            command: "uname -a && free -h && df -h",
            description: "Detaylı sistem bilgilerini görüntüler",
            color: "purple",
            icon: CpuChipIcon,
        },
        {
            name: "🔍 Log İnceleme",
            command: "tail -f /var/log/adxl355.log",
            description: "Canlı log takibi yapar",
            color: "yellow",
            icon: ClockIcon,
        },
        {
            name: "⚙️ Config Görüntüle",
            command: "bash show_config.sh",
            description: "Mevcut yapılandırma ayarlarını gösterir",
            color: "cyan",
            icon: CpuChipIcon,
        },
        {
            name: "🔧 Config Güncelle",
            command: "CONFIG_UPDATE_MODAL",
            description: "Sensör eşik değerlerini günceller",
            color: "indigo",
            icon: CpuChipIcon,
            hasParams: true,
        },
        {
            name: "🎯 Sensör Kalibrasyonu",
            command: 'echo "e" | bash calibrate.sh',
            description: "ADXL355 sensörünü kalibre eder",
            color: "yellow",
            icon: SignalIcon,
        },
        {
            name: "✅ Kalibrasyon Test",
            command: 'echo "e" | bash test_quality.sh',
            description: "Kalibrasyon kalitesini test eder",
            color: "emerald",
            icon: CheckCircleIcon,
        },
        {
            name: "⚡ Sistem Yeniden Başlat",
            command: "sudo reboot",
            description: "Raspberry Pi'yi yeniden başlatır",
            color: "orange",
            icon: PowerIcon,
        },
    ];

    useEffect(() => {
        if (isOpen && inputRef.current) {
            inputRef.current.focus();
        }
    }, [isOpen]);

    useEffect(() => {
        if (outputRef.current) {
            outputRef.current.scrollTop = outputRef.current.scrollHeight;
        }
    }, [output]);

    const executeCommand = async (cmdToExecute = command) => {
        if (!cmdToExecute.trim() || isLoading) return;

        if (cmdToExecute === "CONFIG_UPDATE_MODAL") {
            setShowConfigModal(true);
            return;
        }

        const timestamp = new Date().toLocaleTimeString();
        const newOutput = {
            type: "command",
            content: `pi@${device.name}:~/rasberrypi-python-client$ ${cmdToExecute}`,
            timestamp,
        };

        setOutput((prev) => [...prev, newOutput]);
        setIsLoading(true);

        if (cmdToExecute !== "" && !commandHistory.includes(cmdToExecute)) {
            setCommandHistory((prev) => [cmdToExecute, ...prev.slice(0, 49)]);
        }

        try {
            const response = await fetch(
                route("device.raspberry-pi.console", device.id),
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                    body: JSON.stringify({
                        command: cmdToExecute,
                    }),
                }
            );

            const data = await response.json();

            if (data.success) {
                const outputEntry = {
                    type: "output",
                    content:
                        data.result.output || "Komut başarıyla çalıştırıldı.",
                    timestamp: new Date().toLocaleTimeString(),
                    success: true,
                };
                setOutput((prev) => [...prev, outputEntry]);
            } else {
                const errorEntry = {
                    type: "error",
                    content: `Hata: ${data.error || "Komut çalıştırılamadı"}`,
                    timestamp: new Date().toLocaleTimeString(),
                    success: false,
                };
                setOutput((prev) => [...prev, errorEntry]);
            }
        } catch (error) {
            const errorEntry = {
                type: "error",
                content: `Bağlantı Hatası: ${error.message}`,
                timestamp: new Date().toLocaleTimeString(),
                success: false,
            };
            setOutput((prev) => [...prev, errorEntry]);
        } finally {
            setIsLoading(false);
            setCommand("");
            setHistoryIndex(-1);
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === "Enter") {
            e.preventDefault();
            executeCommand();
        } else if (e.key === "ArrowUp") {
            e.preventDefault();
            if (historyIndex < commandHistory.length - 1) {
                const newIndex = historyIndex + 1;
                setHistoryIndex(newIndex);
                setCommand(commandHistory[newIndex]);
            }
        } else if (e.key === "ArrowDown") {
            e.preventDefault();
            if (historyIndex > 0) {
                const newIndex = historyIndex - 1;
                setHistoryIndex(newIndex);
                setCommand(commandHistory[newIndex]);
            } else if (historyIndex === 0) {
                setHistoryIndex(-1);
                setCommand("");
            }
        }
    };

    const clearConsole = () => {
        setOutput([]);
    };

    const handleConfigParamChange = (e) => {
        const { name, value } = e.target;
        setConfigParams((prev) => ({ ...prev, [name]: value }));
    };

    const executeConfigUpdate = async () => {
        if (!configParams.configKey || !configParams.configValue) {
            return;
        }

        const configCommand = `bash update_config.sh "${configParams.configKey}" "${configParams.configValue}"`;
        setShowConfigModal(false);
        await executeCommand(configCommand);
    };

    const getColorClasses = (color) => {
        const colors = {
            green: "bg-green-500 hover:bg-green-600 border-green-400",
            red: "bg-red-500 hover:bg-red-600 border-red-400",
            blue: "bg-blue-500 hover:bg-blue-600 border-blue-400",
            cyan: "bg-cyan-500 hover:bg-cyan-600 border-cyan-400",
            purple: "bg-purple-500 hover:bg-purple-600 border-purple-400",
            yellow: "bg-yellow-500 hover:bg-yellow-600 border-yellow-400",
            orange: "bg-orange-500 hover:bg-orange-600 border-orange-400",
        };
        return colors[color] || "bg-gray-500 hover:bg-gray-600 border-gray-400";
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-start justify-center min-h-screen px-4 pt-20 pb-20 text-center sm:block sm:p-0">
                <div
                    className="fixed inset-0 transition-opacity bg-gray-900 bg-opacity-75"
                    onClick={onClose}
                ></div>

                <div className="inline-block w-full max-w-5xl mt-16 mb-8 overflow-hidden text-left align-top transition-all transform bg-white dark:bg-gray-800 rounded-xl shadow-2xl">
                    {/* Header */}
                    <div className="bg-gradient-to-r from-gray-800 to-gray-900 px-6 py-4 border-b border-gray-700">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-green-500 rounded-full">
                                    <CommandLineIcon className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-bold text-white">
                                        🖥️ Raspberry Pi Console
                                    </h3>
                                    <p className="text-sm text-gray-300">
                                        {device.name} - {device.ip}:
                                        {device.port}
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-2">
                                <button
                                    onClick={clearConsole}
                                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-700 border border-gray-600 rounded-lg hover:bg-gray-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                                >
                                    🗑️ Temizle
                                </button>
                                <button
                                    onClick={onClose}
                                    className="inline-flex items-center justify-center w-8 h-8 text-gray-400 bg-transparent rounded-lg hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-gray-500"
                                >
                                    <XMarkIcon className="w-5 h-5" />
                                </button>
                            </div>
                        </div>
                    </div>

                    <div className="flex h-[500px]">
                        {/* Quick Commands Sidebar */}
                        <div className="w-80 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-4 overflow-y-auto">
                            <h4 className="text-sm font-bold text-gray-700 dark:text-gray-300 mb-4 uppercase tracking-wide">
                                🚀 Hızlı Komutlar
                            </h4>

                            <div className="space-y-3">
                                {getQuickCommands().map((cmd, index) => {
                                    const IconComponent = cmd.icon;
                                    return (
                                        <button
                                            key={index}
                                            onClick={() =>
                                                executeCommand(cmd.command)
                                            }
                                            disabled={isLoading}
                                            className={`w-full text-left p-3 rounded-lg border-l-4 transition-all duration-200 ${getColorClasses(
                                                cmd.color
                                            )} text-white hover:scale-105 transform disabled:opacity-50 disabled:cursor-not-allowed`}
                                        >
                                            <div className="flex items-center space-x-3">
                                                <IconComponent className="w-5 h-5 flex-shrink-0" />
                                                <div className="flex-1 min-w-0">
                                                    <div className="font-medium text-sm truncate">
                                                        {cmd.name}
                                                    </div>
                                                    <div className="text-xs opacity-90 mt-1">
                                                        {cmd.description}
                                                    </div>
                                                    <div className="text-xs opacity-75 font-mono mt-1 bg-black/20 px-2 py-1 rounded">
                                                        {cmd.command}
                                                    </div>
                                                </div>
                                            </div>
                                        </button>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Console Area */}
                        <div className="flex-1 flex flex-col">
                            {/* Output Area */}
                            <div
                                ref={outputRef}
                                className="flex-1 bg-black text-green-400 font-mono text-sm p-4 overflow-y-auto"
                                style={{
                                    backgroundImage:
                                        "radial-gradient(rgba(0, 255, 0, 0.03) 1px, transparent 1px)",
                                    backgroundSize: "20px 20px",
                                }}
                            >
                                {output.length === 0 ? (
                                    <div className="text-gray-500">
                                        <div className="mb-2">
                                            🎉 Raspberry Pi Console'a Hoş
                                            Geldiniz!
                                        </div>
                                        <div className="mb-2">
                                            💡 Sol panelden hazır komutları
                                            kullanabilir veya aşağıdan özel
                                            komut yazabilirsiniz.
                                        </div>
                                        <div className="mb-4">
                                            ⌨️ Komut geçmişi için ↑ ↓ tuşlarını
                                            kullanın.
                                        </div>
                                        <div className="text-green-400">
                                            pi@{device.name}
                                            :~/rasberrypi-python-client$ █
                                        </div>
                                    </div>
                                ) : (
                                    output.map((entry, index) => (
                                        <div key={index} className="mb-1">
                                            {entry.type === "command" && (
                                                <div className="text-blue-400 flex items-center space-x-2">
                                                    <span>
                                                        [{entry.timestamp}]
                                                    </span>
                                                    <span>{entry.content}</span>
                                                </div>
                                            )}
                                            {entry.type === "output" && (
                                                <div
                                                    className={`ml-4 whitespace-pre-wrap ${
                                                        entry.success === false
                                                            ? "text-red-400"
                                                            : "text-green-300"
                                                    }`}
                                                >
                                                    <div className="flex items-start space-x-2">
                                                        {entry.success ===
                                                        false ? (
                                                            <XCircleIcon className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                                                        ) : (
                                                            <CheckCircleIcon className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                                                        )}
                                                        <span>
                                                            {entry.content}
                                                        </span>
                                                    </div>
                                                </div>
                                            )}
                                            {entry.type === "error" && (
                                                <div className="ml-4 text-red-400 flex items-start space-x-2">
                                                    <XCircleIcon className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                                                    <span>{entry.content}</span>
                                                </div>
                                            )}
                                        </div>
                                    ))
                                )}

                                {isLoading && (
                                    <div className="flex items-center space-x-2 text-yellow-400">
                                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-yellow-400 border-t-transparent"></div>
                                        <span>Komut çalıştırılıyor...</span>
                                    </div>
                                )}
                            </div>

                            {/* Input Area */}
                            <div className="bg-gray-800 border-t border-gray-700 p-4">
                                <div className="flex items-center space-x-3">
                                    <span className="text-green-400 font-mono text-sm">
                                        pi@{device.name}
                                        :~/rasberrypi-python-client$
                                    </span>
                                    <input
                                        ref={inputRef}
                                        type="text"
                                        value={command}
                                        onChange={(e) =>
                                            setCommand(e.target.value)
                                        }
                                        onKeyDown={handleKeyPress}
                                        disabled={isLoading}
                                        placeholder="Komut yazın ve Enter'a basın..."
                                        className="flex-1 bg-transparent text-green-400 font-mono text-sm border-none outline-none placeholder-gray-500 disabled:opacity-50"
                                    />
                                    <button
                                        onClick={() => executeCommand()}
                                        disabled={isLoading || !command.trim()}
                                        className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-green-500 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        {isLoading ? (
                                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                                        ) : (
                                            <PlayIcon className="w-4 h-4 mr-2" />
                                        )}
                                        Çalıştır
                                    </button>
                                </div>

                                {/* Command history hint */}
                                {commandHistory.length > 0 && (
                                    <div className="mt-2 text-xs text-gray-500">
                                        💡 Son komut: {commandHistory[0]} | ↑ ↓
                                        ile geçmiş
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Config Update Modal */}
            {showConfigModal && (
                <div className="fixed inset-0 z-60 overflow-y-auto">
                    <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                        <div
                            className="fixed inset-0 transition-opacity bg-gray-900 bg-opacity-75"
                            onClick={() => setShowConfigModal(false)}
                        ></div>

                        <div className="inline-block w-full max-w-md my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 rounded-xl shadow-xl">
                            {/* Modal Header */}
                            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="flex items-center justify-center w-10 h-10 bg-white/20 rounded-full">
                                            <CpuChipIcon className="w-6 h-6 text-white" />
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-bold text-white">
                                                🔧 Config Güncelle
                                            </h3>
                                            <p className="text-sm text-indigo-100">
                                                Sensör eşik değerlerini
                                                ayarlayın
                                            </p>
                                        </div>
                                    </div>
                                    <button
                                        onClick={() =>
                                            setShowConfigModal(false)
                                        }
                                        className="inline-flex items-center justify-center w-8 h-8 text-indigo-200 bg-transparent rounded-lg hover:bg-white/10 hover:text-white"
                                    >
                                        <XMarkIcon className="w-5 h-5" />
                                    </button>
                                </div>
                            </div>

                            {/* Modal Body */}
                            <div className="p-6">
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Config Parametresi
                                        </label>
                                        <select
                                            name="configKey"
                                            value={configParams.configKey}
                                            onChange={handleConfigParamChange}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                        >
                                            <option value="SEISMIC_MMI_THRESHOLD">
                                                🌊 Sismik MMI Eşiği
                                            </option>
                                            <option value="EARTHQUAKE_MMI_THRESHOLD">
                                                🌋 Deprem MMI Eşiği
                                            </option>
                                            <option value="RELAY_THRESHOLD">
                                                ⚡ Röle Eşiği
                                            </option>
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Yeni Değer
                                        </label>
                                        <input
                                            type="number"
                                            step="0.1"
                                            name="configValue"
                                            value={configParams.configValue}
                                            onChange={handleConfigParamChange}
                                            placeholder="Örn: 5.0"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                        />
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                                        💡 <strong>İpucu:</strong> Eşik
                                        değerleri sensörün ne zaman
                                        tetikleneceğini belirler. Değerler
                                        genellikle 1.0 - 10.0 arasında
                                        olmalıdır.
                                    </div>
                                </div>
                            </div>

                            {/* Modal Footer */}
                            <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4 flex items-center justify-end space-x-3">
                                <button
                                    onClick={() => setShowConfigModal(false)}
                                    className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
                                >
                                    İptal
                                </button>
                                <button
                                    onClick={executeConfigUpdate}
                                    disabled={
                                        !configParams.configKey ||
                                        !configParams.configValue
                                    }
                                    className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    🔧 Config Güncelle
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
