import { useState } from "react";
import { router } from "@inertiajs/react";
import {
    PlayIcon,
    StopIcon,
    TvIcon,
    SignalIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import PrimaryButton from "./PrimaryButton";
import DangerButton from "./DangerButton";
import Toast from "./Toast";

export default function DeviceMonitorControl({ device, className = "" }) {
    const [isStarting, setIsStarting] = useState(false);
    const [isStopping, setIsStopping] = useState(false);
    const [toast, setToast] = useState(null);

    const showToast = (message, type = "success") => {
        setToast({ message, type });
        setTimeout(() => setToast(null), 5000);
    };

    const startMonitoring = async () => {
        setIsStarting(true);
        try {
            const response = await fetch(
                route("device.start-monitoring", device.id),
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            ?.getAttribute("content"),
                    },
                }
            );

            const result = await response.json();

            if (result.success) {
                showToast(
                    "Real-time monitoring started on device display",
                    "success"
                );
            } else {
                showToast(
                    result.error || "Failed to start monitoring",
                    "error"
                );
            }
        } catch (error) {
            showToast("Network error occurred", "error");
        } finally {
            setIsStarting(false);
        }
    };

    const stopMonitoring = async () => {
        setIsStopping(true);
        try {
            const response = await fetch(
                route("device.stop-monitoring", device.id),
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            ?.getAttribute("content"),
                    },
                }
            );

            const result = await response.json();

            if (result.success) {
                showToast("Real-time monitoring stopped", "success");
            } else {
                showToast(result.error || "Failed to stop monitoring", "error");
            }
        } catch (error) {
            showToast("Network error occurred", "error");
        } finally {
            setIsStopping(false);
        }
    };

    return (
        <>
            <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${className}`}>
                <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <TvIcon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Device Monitor Control
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            Control real-time display on {device.name}
                        </p>
                    </div>
                </div>

                <div className="space-y-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                            <SignalIcon className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                            <div className="flex-1">
                                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                                    Real-Time Monitoring
                                </h4>
                                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                    Display live earthquake data directly on the device's monitor including:
                                </p>
                                <ul className="text-sm text-blue-700 dark:text-blue-300 mt-2 space-y-1">
                                    <li>• Acceleration measurements (X, Y, Z axes)</li>
                                    <li>• Richter magnitude analysis</li>
                                    <li>• Alarm status and triggers</li>
                                    <li>• System status and health</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                            <ExclamationTriangleIcon className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5" />
                            <div className="flex-1">
                                <h4 className="text-sm font-medium text-amber-900 dark:text-amber-100">
                                    Important Notes
                                </h4>
                                <ul className="text-sm text-amber-700 dark:text-amber-300 mt-1 space-y-1">
                                    <li>• Ensure device has a connected monitor/display</li>
                                    <li>• Monitoring runs continuously until stopped</li>
                                    <li>• Data refreshes every second</li>
                                    <li>• Use Ctrl+C on device to stop manually</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div className="flex space-x-3">
                        <PrimaryButton
                            onClick={startMonitoring}
                            disabled={isStarting || isStopping}
                            className="flex-1 flex items-center justify-center space-x-2"
                        >
                            {isStarting ? (
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            ) : (
                                <PlayIcon className="w-4 h-4" />
                            )}
                            <span>
                                {isStarting ? "Starting..." : "Start Monitoring"}
                            </span>
                        </PrimaryButton>

                        <DangerButton
                            onClick={stopMonitoring}
                            disabled={isStarting || isStopping}
                            className="flex-1 flex items-center justify-center space-x-2"
                        >
                            {isStopping ? (
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            ) : (
                                <StopIcon className="w-4 h-4" />
                            )}
                            <span>
                                {isStopping ? "Stopping..." : "Stop Monitoring"}
                            </span>
                        </DangerButton>
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded p-3">
                        <strong>Device Info:</strong> {device.name} ({device.ip}:{device.port})
                        <br />
                        <strong>Status:</strong>{" "}
                        <span
                            className={
                                device.is_active
                                    ? "text-green-600 dark:text-green-400"
                                    : "text-red-600 dark:text-red-400"
                            }
                        >
                            {device.is_active ? "Active" : "Inactive"}
                        </span>
                    </div>
                </div>
            </div>

            {toast && (
                <Toast
                    message={toast.message}
                    type={toast.type}
                    onClose={() => setToast(null)}
                />
            )}
        </>
    );
}
