#!/bin/bash

# earthquake-websocket Deployment Script for Hetzner Server
# This script deploys the WebSocket bridge service to your Hetzner server

# Configuration
HETZNER_SERVER_IP="YOUR_HETZNER_IP"  # Replace with your Hetzner server IP
HETZNER_USER="root"                   # Or your server user
PROJECT_DIR="/opt/earthquake-websocket"
SERVICE_NAME="earthquake-websocket"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to run SSH commands
run_ssh() {
    ssh ${HETZNER_USER}@${HETZNER_SERVER_IP} "$1"
}

# Function to copy files
copy_files() {
    scp -r earthquake-websocket/ ${HETZNER_USER}@${HETZNER_SERVER_IP}:${PROJECT_DIR}/
}

print_header "Deploying earthquake-websocket to Hetzner Server"

# 1. Test SSH connection
print_status "Testing SSH connection to Hetzner server..."
if ! run_ssh "echo 'SSH connection successful'"; then
    print_error "SSH connection failed. Please check your connection."
    exit 1
fi

# 2. Create project directory
print_status "Creating project directory..."
run_ssh "mkdir -p ${PROJECT_DIR}"

# 3. Copy project files
print_status "Copying project files..."
copy_files

# 4. Install Node.js and Docker if not installed
print_status "Installing dependencies..."
run_ssh "
    # Update system
    apt update
    
    # Install Node.js 18
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
    
    # Install Docker if not installed
    if ! command -v docker &> /dev/null; then
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
        systemctl enable docker
        systemctl start docker
    fi
    
    # Install Docker Compose if not installed
    if ! command -v docker-compose &> /dev/null; then
        curl -L \"https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
    fi
"

# 5. Setup environment and install dependencies
print_status "Setting up environment..."
run_ssh "
    cd ${PROJECT_DIR}
    
    # Install npm dependencies
    npm install
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        cat > .env << 'EOF'
NODE_ENV=production
KAFKA_BROKERS=kafka.niny.tech:9093
KAFKA_SECURITY_PROTOCOL=SASL_PLAINTEXT
KAFKA_SASL_MECHANISM=SCRAM-SHA-512
KAFKA_SASL_USERNAME=user
KAFKA_SASL_PASSWORD=Fatih123
KAFKA_TOPICS=pi_001,pi_007,pi_008,pi_009,pi_010,pi_011,balikesir_pi_001
WS_PORT=8888
HTTP_PORT=3001
TOKEN=31mif5cvZUp/RPNTIOlAz+/wqvjg6spsSIfnsoAj7DI=
KAFKA_GROUP_ID=ws-consumer-group
EOF
    fi
"

# 6. Create systemd service
print_status "Creating systemd service..."
run_ssh "
    cat > /etc/systemd/system/${SERVICE_NAME}.service << 'EOF'
[Unit]
Description=Earthquake WebSocket Bridge Service
After=network.target docker.service
Requires=docker.service

[Service]
Type=simple
User=root
WorkingDirectory=${PROJECT_DIR}
ExecStart=/usr/bin/node src/index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=${SERVICE_NAME}

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable ${SERVICE_NAME}
"

# 7. Start the service
print_status "Starting earthquake-websocket service..."
run_ssh "
    cd ${PROJECT_DIR}
    systemctl start ${SERVICE_NAME}
    sleep 3
    systemctl status ${SERVICE_NAME} --no-pager -l
"

# 8. Setup firewall rules
print_status "Configuring firewall..."
run_ssh "
    # Allow WebSocket port
    ufw allow 8888/tcp comment 'Earthquake WebSocket'
    
    # Allow HTTP API port
    ufw allow 3001/tcp comment 'Earthquake HTTP API'
    
    # Show firewall status
    ufw status
"

# 9. Verify deployment
print_status "Verifying deployment..."
run_ssh "
    # Check if service is running
    systemctl is-active ${SERVICE_NAME}
    
    # Check if ports are listening
    netstat -tlnp | grep -E ':(8888|3001)'
    
    # Test HTTP API
    curl -s http://localhost:3001/api/status || echo 'API not ready yet'
"

print_header "Deployment Summary"
echo "🌐 WebSocket Server: ws://${HETZNER_SERVER_IP}:8888"
echo "🔗 HTTP API: http://${HETZNER_SERVER_IP}:3001"
echo "📊 Status Check: curl http://${HETZNER_SERVER_IP}:3001/api/status"
echo "📝 Service Logs: ssh ${HETZNER_USER}@${HETZNER_SERVER_IP} 'journalctl -u ${SERVICE_NAME} -f'"
echo "🔄 Restart Service: ssh ${HETZNER_USER}@${HETZNER_SERVER_IP} 'systemctl restart ${SERVICE_NAME}'"

print_status "Deployment completed successfully!"
print_warning "Don't forget to update your dashboard configuration to point to:"
print_warning "WebSocket URL: ws://${HETZNER_SERVER_IP}:8888"
