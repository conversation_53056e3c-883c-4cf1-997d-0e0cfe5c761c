<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gelişmiş Deprem İzleme Sistemi</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <script src="dashboard-config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
            background: var(--background-gradient, linear-gradient(135deg, #1e3c72 0%, #2a5298 100%));
            color: #ffffff;
            min-height: 100vh;
        }

        :root {
            --primary-color: #4CAF50;
            --warning-color: #FF9800;
            --danger-color: #f44336;
            --background-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            --panel-opacity: 0.1;
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            position: relative;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .header-info {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .connection-status {
            display: inline-block;
            padding: 0.3rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .connected {
            background: var(--primary-color);
            color: white;
        }

        .disconnected {
            background: var(--danger-color);
            color: white;
        }

        .settings-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .settings-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 1rem;
            padding: 1rem;
            height: calc(100vh - 120px);
        }

        .panel {
            background: rgba(255, 255, 255, var(--panel-opacity));
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow-y: auto;
        }

        .panel h3 {
            margin-bottom: 1rem;
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .device-info {
            display: grid;
            gap: 0.5rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .alarm-status {
            text-align: center;
            padding: 1rem;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.2rem;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }

        .alarm-normal {
            background: var(--primary-color);
        }

        .alarm-seismic {
            background: var(--warning-color);
            animation: pulse 2s infinite;
        }

        .alarm-earthquake {
            background: var(--danger-color);
            animation: shake 0.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .chart-container {
            height: 400px;
            margin: 1rem 0;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.5rem;
        }

        .acceleration-display {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .axis-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .axis-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .axis-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .axis-x { color: #FF6B6B; }
        .axis-y { color: #4ECDC4; }
        .axis-z { color: #45B7D1; }

        .log-container {
            height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.2rem;
            border-left: 3px solid var(--primary-color);
            padding-left: 0.5rem;
            animation: fadeIn 0.3s ease;
        }

        .log-error {
            border-left-color: var(--danger-color);
            color: #ffcdd2;
        }

        .log-warning {
            border-left-color: var(--warning-color);
            color: #ffe0b2;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(-10px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto auto;
            }
            
            .panel:nth-child(2) {
                grid-column: 1 / -1;
            }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                gap: 0.5rem;
                padding: 0.5rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .header-info {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .acceleration-display {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 250px;
            }
        }

        /* Settings Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background: var(--background-gradient);
            margin: 5% auto;
            padding: 2rem;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: white;
        }

        .settings-section {
            margin-bottom: 2rem;
        }

        .settings-section h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }

        .setting-item input,
        .setting-item select {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.3rem;
            border-radius: 3px;
        }

        .setting-item input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="header">
        <button class="settings-btn" onclick="openSettings()">⚙️</button>
        <h1 id="pageTitle">🌍 Gelişmiş Deprem İzleme Sistemi</h1>
        <div class="header-info">
            <span id="deviceId">Cihaz: Bağlanıyor...</span>
            <span id="connectionStatus" class="connection-status disconnected">Bağlantı Kesildi</span>
            <span id="lastUpdate">Son Güncelleme: -</span>
        </div>
    </div>

    <div class="dashboard">
        <!-- Sol Panel - Cihaz Bilgileri -->
        <div class="panel">
            <h3>📡 <span id="deviceStatusTitle">Cihaz Durumu</span></h3>
            <div class="device-info">
                <div class="info-item">
                    <span>Cihaz ID:</span>
                    <span id="deviceIdDisplay">-</span>
                </div>
                <div class="info-item">
                    <span>Örnek Sayısı:</span>
                    <span id="sampleCount">-</span>
                </div>
                <div class="info-item">
                    <span>Zaman Damgası:</span>
                    <span id="timestamp">-</span>
                </div>
            </div>

            <div id="alarmStatus" class="alarm-status alarm-normal">
                NORMAL
            </div>

            <h3>📊 <span id="accelerationTitle">İvme Verileri (g)</span></h3>
            <div class="acceleration-display">
                <div class="axis-card">
                    <div class="axis-value axis-x" id="accelX">0.000</div>
                    <div>X Ekseni</div>
                </div>
                <div class="axis-card">
                    <div class="axis-value axis-y" id="accelY">0.000</div>
                    <div>Y Ekseni</div>
                </div>
                <div class="axis-card">
                    <div class="axis-value axis-z" id="accelZ">0.000</div>
                    <div>Z Ekseni</div>
                </div>
            </div>

            <h3>🔧 Kalibrasyon</h3>
            <div class="device-info">
                <div class="info-item">
                    <span>X Offset:</span>
                    <span id="calibX">-</span>
                </div>
                <div class="info-item">
                    <span>Y Offset:</span>
                    <span id="calibY">-</span>
                </div>
                <div class="info-item">
                    <span>Z Offset:</span>
                    <span id="calibZ">-</span>
                </div>
            </div>
        </div>

        <!-- Orta Panel - Grafikler -->
        <div class="panel">
            <h3>📈 Gerçek Zamanlı İvme Grafiği</h3>
            <div id="accelerationChart" class="chart-container"></div>

            <h3>📊 <span id="seismicAnalysisTitle">Sismik Analiz Metrikleri</span></h3>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="magnitude">0.000</div>
                    <div class="metric-label">Büyüklük (g)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="mmiScale">0.0</div>
                    <div class="metric-label">MMI Ölçeği</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="richterMag">0.0</div>
                    <div class="metric-label">Richter Büyüklüğü</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="pgaValue">0.000</div>
                    <div class="metric-label">PGA (g)</div>
                </div>
            </div>
        </div>

        <!-- Sağ Panel - Analiz ve Loglar -->
        <div class="panel">
            <h3>🔍 Detaylı Analiz</h3>
            <div class="device-info">
                <div class="info-item">
                    <span>X PGA:</span>
                    <span id="pgaX">-</span>
                </div>
                <div class="info-item">
                    <span>Y PGA:</span>
                    <span id="pgaY">-</span>
                </div>
                <div class="info-item">
                    <span>Z PGA:</span>
                    <span id="pgaZ">-</span>
                </div>
                <div class="info-item">
                    <span>R PGA:</span>
                    <span id="pgaR">-</span>
                </div>
            </div>

            <h3>📊 Filtre Yüzdeleri</h3>
            <div class="device-info">
                <div class="info-item">
                    <span>X Filtre:</span>
                    <span id="filterX">-</span>
                </div>
                <div class="info-item">
                    <span>Y Filtre:</span>
                    <span id="filterY">-</span>
                </div>
                <div class="info-item">
                    <span>Z Filtre:</span>
                    <span id="filterZ">-</span>
                </div>
                <div class="info-item">
                    <span>Ortalama:</span>
                    <span id="filterAvg">-</span>
                </div>
            </div>

            <h3>📝 Sistem Logları</h3>
            <div id="logContainer" class="log-container">
                <div class="log-entry">Sistem başlatılıyor...</div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeSettings()">&times;</span>
            <h2>⚙️ Dashboard Ayarları</h2>

            <div class="settings-section">
                <h4>🔗 Bağlantı Ayarları</h4>
                <div class="setting-item">
                    <label>WebSocket URL:</label>
                    <input type="text" id="websocketUrl" placeholder="ws://localhost:8888">
                </div>
                <div class="setting-item">
                    <label>JWT Token:</label>
                    <input type="text" id="jwtToken" placeholder="your-jwt-token-here">
                </div>
            </div>

            <div class="settings-section">
                <h4>📊 Grafik Ayarları</h4>
                <div class="setting-item">
                    <label>Maksimum Veri Noktası:</label>
                    <input type="number" id="maxDataPoints" min="50" max="500" value="100">
                </div>
                <div class="setting-item">
                    <label>Güncelleme Sıklığı (ms):</label>
                    <input type="number" id="updateInterval" min="500" max="5000" value="1000">
                </div>
            </div>

            <div class="settings-section">
                <h4>🚨 Alarm Ayarları</h4>
                <div class="setting-item">
                    <label>Ses Uyarısı:</label>
                    <input type="checkbox" id="soundEnabled">
                </div>
                <div class="setting-item">
                    <label>Titreşim Efekti:</label>
                    <input type="checkbox" id="shakeEnabled" checked>
                </div>
            </div>

            <div class="settings-section">
                <h4>🎨 Tema Ayarları</h4>
                <div class="setting-item">
                    <label>Ana Renk:</label>
                    <input type="color" id="primaryColor" value="#4CAF50">
                </div>
                <div class="setting-item">
                    <label>Uyarı Rengi:</label>
                    <input type="color" id="warningColor" value="#FF9800">
                </div>
                <div class="setting-item">
                    <label>Tehlike Rengi:</label>
                    <input type="color" id="dangerColor" value="#f44336">
                </div>
            </div>

            <div class="settings-section">
                <h4>🧪 Test Modu</h4>
                <div class="setting-item">
                    <label>Test Modu Aktif:</label>
                    <input type="checkbox" id="testModeEnabled">
                </div>
            </div>

            <div style="text-align: center; margin-top: 2rem;">
                <button onclick="saveSettings()" style="background: var(--primary-color); color: white; border: none; padding: 1rem 2rem; border-radius: 5px; cursor: pointer; margin-right: 1rem;">Kaydet</button>
                <button onclick="resetSettings()" style="background: var(--danger-color); color: white; border: none; padding: 1rem 2rem; border-radius: 5px; cursor: pointer;">Sıfırla</button>
            </div>
        </div>
    </div>

    <script>
        // Global değişkenler
        let ws = null;
        let isConnected = false;
        let accelerationData = {
            x: [],
            y: [],
            z: [],
            timestamps: []
        };
        let reconnectAttempts = 0;
        let testModeInterval = null;

        // Konfigürasyonu yükle
        function loadConfig() {
            ConfigHelper.load();
            applyTheme();
            updateSettingsUI();
        }

        // Tema uygula
        function applyTheme() {
            const root = document.documentElement;
            const config = DASHBOARD_CONFIG.ui.theme;

            root.style.setProperty('--primary-color', config.primaryColor);
            root.style.setProperty('--warning-color', config.warningColor);
            root.style.setProperty('--danger-color', config.dangerColor);
            root.style.setProperty('--background-gradient', config.backgroundGradient);
            root.style.setProperty('--panel-opacity', config.panelOpacity);
            root.style.setProperty('--font-family', config.fontFamily);
        }

        // Settings UI'ı güncelle
        function updateSettingsUI() {
            document.getElementById('websocketUrl').value = DASHBOARD_CONFIG.connection.websocketUrl;
            document.getElementById('jwtToken').value = DASHBOARD_CONFIG.connection.jwtToken;
            document.getElementById('maxDataPoints').value = DASHBOARD_CONFIG.chart.maxDataPoints;
            document.getElementById('updateInterval').value = DASHBOARD_CONFIG.chart.updateInterval;
            document.getElementById('soundEnabled').checked = DASHBOARD_CONFIG.alarms.effects.soundEnabled;
            document.getElementById('shakeEnabled').checked = DASHBOARD_CONFIG.alarms.effects.shakeEnabled;
            document.getElementById('primaryColor').value = DASHBOARD_CONFIG.ui.theme.primaryColor;
            document.getElementById('warningColor').value = DASHBOARD_CONFIG.ui.theme.warningColor;
            document.getElementById('dangerColor').value = DASHBOARD_CONFIG.ui.theme.dangerColor;
            document.getElementById('testModeEnabled').checked = DASHBOARD_CONFIG.testMode.enabled;
        }

        // Settings modal açma/kapama
        function openSettings() {
            document.getElementById('settingsModal').style.display = 'block';
        }

        function closeSettings() {
            document.getElementById('settingsModal').style.display = 'none';
        }

        // Ayarları kaydet
        function saveSettings() {
            DASHBOARD_CONFIG.connection.websocketUrl = document.getElementById('websocketUrl').value;
            DASHBOARD_CONFIG.connection.jwtToken = document.getElementById('jwtToken').value;
            DASHBOARD_CONFIG.chart.maxDataPoints = parseInt(document.getElementById('maxDataPoints').value);
            DASHBOARD_CONFIG.chart.updateInterval = parseInt(document.getElementById('updateInterval').value);
            DASHBOARD_CONFIG.alarms.effects.soundEnabled = document.getElementById('soundEnabled').checked;
            DASHBOARD_CONFIG.alarms.effects.shakeEnabled = document.getElementById('shakeEnabled').checked;
            DASHBOARD_CONFIG.ui.theme.primaryColor = document.getElementById('primaryColor').value;
            DASHBOARD_CONFIG.ui.theme.warningColor = document.getElementById('warningColor').value;
            DASHBOARD_CONFIG.ui.theme.dangerColor = document.getElementById('dangerColor').value;
            DASHBOARD_CONFIG.testMode.enabled = document.getElementById('testModeEnabled').checked;

            ConfigHelper.save();
            applyTheme();
            closeSettings();
            addLog('Ayarlar kaydedildi', 'info');

            // Bağlantıyı yeniden başlat
            if (ws) {
                ws.close();
            }
            setTimeout(connectWebSocket, 1000);
        }

        // Ayarları sıfırla
        function resetSettings() {
            if (confirm('Tüm ayarlar sıfırlanacak. Emin misiniz?')) {
                ConfigHelper.reset();
            }
        }

        // WebSocket bağlantısını başlat
        function connectWebSocket() {
            if (DASHBOARD_CONFIG.testMode.enabled) {
                addLog('Test modu aktif, gerçek bağlantı kurulmayacak', 'warning');
                startTestData();
                return;
            }

            const wsUrl = DASHBOARD_CONFIG.connection.websocketUrl;
            const token = DASHBOARD_CONFIG.connection.jwtToken;

            if (!token || token === 'your-jwt-token-here') {
                addLog('JWT token gerekli. Lütfen ayarlardan token\'ı girin.', 'error');
                return;
            }

            try {
                ws = new WebSocket(`${wsUrl}?token=${token}`);

                ws.onopen = function() {
                    isConnected = true;
                    reconnectAttempts = 0;
                    updateConnectionStatus(true);
                    addLog('WebSocket bağlantısı kuruldu', 'info');
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleIncomingData(data);
                    } catch (error) {
                        addLog(`Veri parse hatası: ${error.message}`, 'error');
                    }
                };

                ws.onclose = function() {
                    isConnected = false;
                    updateConnectionStatus(false);
                    addLog('WebSocket bağlantısı kesildi', 'warning');

                    // Yeniden bağlanmaya çalış
                    if (reconnectAttempts < DASHBOARD_CONFIG.connection.maxReconnectAttempts) {
                        reconnectAttempts++;
                        addLog(`Yeniden bağlanma denemesi ${reconnectAttempts}/${DASHBOARD_CONFIG.connection.maxReconnectAttempts}`, 'info');
                        setTimeout(connectWebSocket, DASHBOARD_CONFIG.connection.reconnectInterval);
                    } else {
                        addLog('Maksimum yeniden bağlanma denemesi aşıldı, test moduna geçiliyor', 'warning');
                        startTestData();
                    }
                };

                ws.onerror = function(error) {
                    addLog(`WebSocket hatası: ${error}`, 'error');
                };

            } catch (error) {
                addLog(`Bağlantı hatası: ${error.message}`, 'error');
                setTimeout(connectWebSocket, DASHBOARD_CONFIG.connection.reconnectInterval);
            }
        }

        // Gelen veriyi işle
        function handleIncomingData(data) {
            if (data.type === 'new_earthquake' && data.payload) {
                updateDashboard(data.payload);
                addLog(`Yeni veri alındı: ${data.payload.device_id}`, 'info');
            } else if (data.type === 'mcp_earthquake_alert') {
                addLog(`🚨 MCP ALARM: ${data.source_device?.name || 'Bilinmiyor'}`, 'error');
                // MCP alarm için özel işlemler
            }
        }

        // Dashboard'u güncelle
        function updateDashboard(sensorData) {
            // Cihaz bilgileri
            document.getElementById('deviceIdDisplay').textContent = sensorData.device_id || '-';
            document.getElementById('deviceId').textContent = `Cihaz: ${sensorData.device_id || 'Bilinmiyor'}`;
            document.getElementById('sampleCount').textContent = sensorData.sample_count || '-';
            document.getElementById('timestamp').textContent = formatTimestamp(sensorData.timestamp);
            document.getElementById('lastUpdate').textContent = `Son Güncelleme: ${moment().format('HH:mm:ss')}`;

            // İvme verileri
            const accel = sensorData.current_acceleration || {};
            const decimals = DASHBOARD_CONFIG.dataFormat.decimals.acceleration;
            document.getElementById('accelX').textContent = (accel.x || 0).toFixed(decimals);
            document.getElementById('accelY').textContent = (accel.y || 0).toFixed(decimals);
            document.getElementById('accelZ').textContent = (accel.z || 0).toFixed(decimals);

            // Analiz verileri
            const analysis = sensorData.analysis || {};
            document.getElementById('magnitude').textContent = (analysis.magnitude || 0).toFixed(DASHBOARD_CONFIG.dataFormat.decimals.magnitude);
            document.getElementById('mmiScale').textContent = (analysis.mmi_scale || 0).toFixed(DASHBOARD_CONFIG.dataFormat.decimals.mmi);
            document.getElementById('richterMag').textContent = (analysis.richter_magnitude || 0).toFixed(DASHBOARD_CONFIG.dataFormat.decimals.richter);

            // PGA verileri
            const pga = analysis.pga || {};
            const pgaDecimals = DASHBOARD_CONFIG.dataFormat.decimals.pga;
            document.getElementById('pgaValue').textContent = (pga.r_pga || 0).toFixed(pgaDecimals);
            document.getElementById('pgaX').textContent = (pga.x_pga || 0).toFixed(pgaDecimals);
            document.getElementById('pgaY').textContent = (pga.y_pga || 0).toFixed(pgaDecimals);
            document.getElementById('pgaZ').textContent = (pga.z_pga || 0).toFixed(pgaDecimals);
            document.getElementById('pgaR').textContent = (pga.r_pga || 0).toFixed(pgaDecimals);

            // Kalibrasyon verileri
            const calibration = sensorData.calibration || {};
            const calibDecimals = DASHBOARD_CONFIG.dataFormat.decimals.calibration;
            document.getElementById('calibX').textContent = (calibration.x_offset || 0).toFixed(calibDecimals);
            document.getElementById('calibY').textContent = (calibration.y_offset || 0).toFixed(calibDecimals);
            document.getElementById('calibZ').textContent = (calibration.z_offset || 0).toFixed(calibDecimals);

            // Filtre yüzdeleri
            const percentDecimals = DASHBOARD_CONFIG.dataFormat.decimals.percentage;
            document.getElementById('filterX').textContent = `${(analysis.x_filter_percentage || 0).toFixed(percentDecimals)}%`;
            document.getElementById('filterY').textContent = `${(analysis.y_filter_percentage || 0).toFixed(percentDecimals)}%`;
            document.getElementById('filterZ').textContent = `${(analysis.z_filter_percentage || 0).toFixed(percentDecimals)}%`;
            document.getElementById('filterAvg').textContent = `${(analysis.avg_filter_percentage || 0).toFixed(percentDecimals)}%`;

            // Alarm durumu
            updateAlarmStatus(sensorData.alarms || {});

            // Grafik güncelle
            updateChart(accel);
        }

        // Alarm durumunu güncelle
        function updateAlarmStatus(alarms) {
            const statusElement = document.getElementById('alarmStatus');
            const config = DASHBOARD_CONFIG.alarms;

            if (alarms.earthquake_alarm) {
                statusElement.textContent = '🚨 DEPREM ALARMI';
                statusElement.className = 'alarm-status alarm-earthquake';
                addLog('DEPREM ALARMI AKTIF!', 'error');

                if (config.effects.soundEnabled) {
                    playAlarmSound();
                }
            } else if (alarms.seismic_alarm) {
                statusElement.textContent = '⚠️ SİSMİK AKTİVİTE';
                statusElement.className = 'alarm-status alarm-seismic';
                addLog('Sismik aktivite tespit edildi', 'warning');
            } else {
                statusElement.textContent = '✅ NORMAL';
                statusElement.className = 'alarm-status alarm-normal';
            }
        }

        // Alarm sesi çal
        function playAlarmSound() {
            if (DASHBOARD_CONFIG.alarms.effects.soundEnabled && DASHBOARD_CONFIG.alarms.effects.soundFile) {
                try {
                    const audio = new Audio(DASHBOARD_CONFIG.alarms.effects.soundFile);
                    audio.play().catch(e => {
                        addLog('Alarm sesi çalınamadı: ' + e.message, 'warning');
                    });
                } catch (error) {
                    addLog('Alarm sesi hatası: ' + error.message, 'error');
                }
            }
        }

        // Bağlantı durumunu güncelle
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            if (connected) {
                statusElement.textContent = 'Bağlı';
                statusElement.className = 'connection-status connected';
            } else {
                statusElement.textContent = 'Bağlantı Kesildi';
                statusElement.className = 'connection-status disconnected';
            }
        }

        // Zaman damgasını formatla
        function formatTimestamp(timestamp) {
            if (!timestamp) return '-';
            return moment.unix(timestamp).format(DASHBOARD_CONFIG.dataFormat.timestampFormat);
        }

        // Log ekle
        function addLog(message, type = 'info') {
            if (!DASHBOARD_CONFIG.logging.levels[type]) return;

            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type === 'error' ? 'log-error' : type === 'warning' ? 'log-warning' : ''}`;
            logEntry.textContent = `${moment().format(DASHBOARD_CONFIG.logging.timestampFormat)} - ${message}`;

            logContainer.appendChild(logEntry);

            if (DASHBOARD_CONFIG.logging.autoScroll) {
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            // Maksimum log sayısını kontrol et
            while (logContainer.children.length > DASHBOARD_CONFIG.logging.maxLogEntries) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // Grafik güncelle
        function updateChart(accel) {
            const now = new Date();
            const maxPoints = DASHBOARD_CONFIG.chart.maxDataPoints;

            // Veri ekle
            accelerationData.x.push(accel.x || 0);
            accelerationData.y.push(accel.y || 0);
            accelerationData.z.push(accel.z || 0);
            accelerationData.timestamps.push(now);

            // Maksimum veri noktası sınırı
            if (accelerationData.x.length > maxPoints) {
                accelerationData.x.shift();
                accelerationData.y.shift();
                accelerationData.z.shift();
                accelerationData.timestamps.shift();
            }

            // Plotly grafiğini güncelle
            const update = {
                x: [accelerationData.timestamps, accelerationData.timestamps, accelerationData.timestamps],
                y: [accelerationData.x, accelerationData.y, accelerationData.z]
            };

            Plotly.redraw('accelerationChart', update);
        }

        // Grafik başlat
        function initializeChart() {
            const colors = DASHBOARD_CONFIG.chart.colors;

            const data = [
                {
                    x: [],
                    y: [],
                    type: 'scatter',
                    mode: 'lines',
                    name: 'X Ekseni',
                    line: { color: colors.xAxis, width: 2 }
                },
                {
                    x: [],
                    y: [],
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Y Ekseni',
                    line: { color: colors.yAxis, width: 2 }
                },
                {
                    x: [],
                    y: [],
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Z Ekseni',
                    line: { color: colors.zAxis, width: 2 }
                }
            ];

            const layout = {
                title: {
                    text: 'İvme Verileri (g)',
                    font: { color: '#ffffff' }
                },
                xaxis: {
                    title: 'Zaman',
                    color: '#ffffff',
                    gridcolor: 'rgba(255,255,255,0.2)'
                },
                yaxis: {
                    title: 'İvme (g)',
                    color: '#ffffff',
                    gridcolor: 'rgba(255,255,255,0.2)'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0.3)',
                font: { color: '#ffffff' },
                legend: {
                    font: { color: '#ffffff' }
                },
                margin: { t: 50, r: 50, b: 50, l: 50 }
            };

            const config = {
                responsive: true,
                displayModeBar: false
            };

            Plotly.newPlot('accelerationChart', data, layout, config);
        }

        // Test verisi başlat
        function startTestData() {
            if (testModeInterval) {
                clearInterval(testModeInterval);
            }

            updateConnectionStatus(false);
            addLog('Test modu başlatıldı', 'info');

            testModeInterval = setInterval(() => {
                const ranges = DASHBOARD_CONFIG.testMode.ranges;
                const alarmProb = DASHBOARD_CONFIG.testMode.alarmProbability;

                const testData = {
                    device_id: 'pi_test_001',
                    current_acceleration: {
                        x: ranges.acceleration.x[0] + Math.random() * (ranges.acceleration.x[1] - ranges.acceleration.x[0]),
                        y: ranges.acceleration.y[0] + Math.random() * (ranges.acceleration.y[1] - ranges.acceleration.y[0]),
                        z: ranges.acceleration.z[0] + Math.random() * (ranges.acceleration.z[1] - ranges.acceleration.z[0])
                    },
                    analysis: {
                        magnitude: ranges.magnitude[0] + Math.random() * (ranges.magnitude[1] - ranges.magnitude[0]),
                        mmi_scale: ranges.mmi[0] + Math.random() * (ranges.mmi[1] - ranges.mmi[0]),
                        richter_magnitude: ranges.richter[0] + Math.random() * (ranges.richter[1] - ranges.richter[0]),
                        pga: {
                            x_pga: Math.random() * 0.01,
                            y_pga: Math.random() * 0.01,
                            z_pga: Math.random() * 0.01,
                            r_pga: Math.random() * 0.02
                        },
                        x_filter_percentage: 85 + Math.random() * 10,
                        y_filter_percentage: 85 + Math.random() * 10,
                        z_filter_percentage: 85 + Math.random() * 10,
                        avg_filter_percentage: 85 + Math.random() * 10
                    },
                    alarms: {
                        earthquake_alarm: Math.random() < alarmProb.earthquake,
                        seismic_alarm: Math.random() < alarmProb.seismic
                    },
                    calibration: {
                        x_offset: (Math.random() - 0.5) * 0.001,
                        y_offset: (Math.random() - 0.5) * 0.001,
                        z_offset: (Math.random() - 0.5) * 0.001
                    },
                    sample_count: Math.floor(Math.random() * 1000) + 1000,
                    timestamp: Math.floor(Date.now() / 1000)
                };

                updateDashboard(testData);
            }, DASHBOARD_CONFIG.testMode.updateInterval);
        }

        // Modal dışına tıklandığında kapat
        window.onclick = function(event) {
            const modal = document.getElementById('settingsModal');
            if (event.target === modal) {
                closeSettings();
            }
        }

        // Sayfa yüklendiğinde başlat
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Sistem başlatılıyor...', 'info');
            loadConfig();
            initializeChart();

            // Bağlantıyı başlat
            setTimeout(() => {
                connectWebSocket();
            }, 1000);
        });

        // Sayfa kapatılırken temizlik
        window.addEventListener('beforeunload', function() {
            if (ws) {
                ws.close();
            }
            if (testModeInterval) {
                clearInterval(testModeInterval);
            }
        });
    </script>
</body>
</html>
