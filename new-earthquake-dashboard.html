<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gelişmiş Deprem İzleme Sistemi</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid #4CAF50;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .connection-status {
            display: inline-block;
            padding: 0.3rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 1rem;
        }

        .connected {
            background: #4CAF50;
            color: white;
        }

        .disconnected {
            background: #f44336;
            color: white;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 1rem;
            padding: 1rem;
            height: calc(100vh - 120px);
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel h3 {
            margin-bottom: 1rem;
            color: #4CAF50;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 0.5rem;
        }

        .device-info {
            display: grid;
            gap: 0.5rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }

        .alarm-status {
            text-align: center;
            padding: 1rem;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.2rem;
            margin: 1rem 0;
        }

        .alarm-normal {
            background: #4CAF50;
        }

        .alarm-seismic {
            background: #FF9800;
            animation: pulse 2s infinite;
        }

        .alarm-earthquake {
            background: #f44336;
            animation: shake 0.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .chart-container {
            height: 400px;
            margin: 1rem 0;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.5rem;
        }

        .acceleration-display {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .axis-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .axis-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .axis-x { color: #FF6B6B; }
        .axis-y { color: #4ECDC4; }
        .axis-z { color: #45B7D1; }

        .log-container {
            height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.2rem;
            border-left: 3px solid #4CAF50;
            padding-left: 0.5rem;
        }

        .log-error {
            border-left-color: #f44336;
            color: #ffcdd2;
        }

        .log-warning {
            border-left-color: #FF9800;
            color: #ffe0b2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌍 Gelişmiş Deprem İzleme Sistemi</h1>
        <span id="deviceId">Cihaz: Bağlanıyor...</span>
        <span id="connectionStatus" class="connection-status disconnected">Bağlantı Kesildi</span>
        <span id="lastUpdate">Son Güncelleme: -</span>
    </div>

    <div class="dashboard">
        <!-- Sol Panel - Cihaz Bilgileri -->
        <div class="panel">
            <h3>📡 Cihaz Durumu</h3>
            <div class="device-info">
                <div class="info-item">
                    <span>Cihaz ID:</span>
                    <span id="deviceIdDisplay">-</span>
                </div>
                <div class="info-item">
                    <span>Örnek Sayısı:</span>
                    <span id="sampleCount">-</span>
                </div>
                <div class="info-item">
                    <span>Zaman Damgası:</span>
                    <span id="timestamp">-</span>
                </div>
            </div>

            <div id="alarmStatus" class="alarm-status alarm-normal">
                NORMAL
            </div>

            <h3>📊 İvme Verileri (g)</h3>
            <div class="acceleration-display">
                <div class="axis-card">
                    <div class="axis-value axis-x" id="accelX">0.000</div>
                    <div>X Ekseni</div>
                </div>
                <div class="axis-card">
                    <div class="axis-value axis-y" id="accelY">0.000</div>
                    <div>Y Ekseni</div>
                </div>
                <div class="axis-card">
                    <div class="axis-value axis-z" id="accelZ">0.000</div>
                    <div>Z Ekseni</div>
                </div>
            </div>

            <h3>🔧 Kalibrasyon</h3>
            <div class="device-info">
                <div class="info-item">
                    <span>X Offset:</span>
                    <span id="calibX">-</span>
                </div>
                <div class="info-item">
                    <span>Y Offset:</span>
                    <span id="calibY">-</span>
                </div>
                <div class="info-item">
                    <span>Z Offset:</span>
                    <span id="calibZ">-</span>
                </div>
            </div>
        </div>

        <!-- Orta Panel - Grafikler -->
        <div class="panel">
            <h3>📈 Gerçek Zamanlı İvme Grafiği</h3>
            <div id="accelerationChart" class="chart-container"></div>

            <h3>📊 Sismik Analiz Metrikleri</h3>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="magnitude">0.000</div>
                    <div class="metric-label">Büyüklük (g)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="mmiScale">0.0</div>
                    <div class="metric-label">MMI Ölçeği</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="richterMag">0.0</div>
                    <div class="metric-label">Richter Büyüklüğü</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="pgaValue">0.000</div>
                    <div class="metric-label">PGA (g)</div>
                </div>
            </div>
        </div>

        <!-- Sağ Panel - Analiz ve Loglar -->
        <div class="panel">
            <h3>🔍 Detaylı Analiz</h3>
            <div class="device-info">
                <div class="info-item">
                    <span>X PGA:</span>
                    <span id="pgaX">-</span>
                </div>
                <div class="info-item">
                    <span>Y PGA:</span>
                    <span id="pgaY">-</span>
                </div>
                <div class="info-item">
                    <span>Z PGA:</span>
                    <span id="pgaZ">-</span>
                </div>
                <div class="info-item">
                    <span>R PGA:</span>
                    <span id="pgaR">-</span>
                </div>
            </div>

            <h3>📊 Filtre Yüzdeleri</h3>
            <div class="device-info">
                <div class="info-item">
                    <span>X Filtre:</span>
                    <span id="filterX">-</span>
                </div>
                <div class="info-item">
                    <span>Y Filtre:</span>
                    <span id="filterY">-</span>
                </div>
                <div class="info-item">
                    <span>Z Filtre:</span>
                    <span id="filterZ">-</span>
                </div>
                <div class="info-item">
                    <span>Ortalama:</span>
                    <span id="filterAvg">-</span>
                </div>
            </div>

            <h3>📝 Sistem Logları</h3>
            <div id="logContainer" class="log-container">
                <div class="log-entry">Sistem başlatılıyor...</div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket bağlantısı ve veri yönetimi
        let ws = null;
        let isConnected = false;
        let accelerationData = {
            x: [],
            y: [],
            z: [],
            timestamps: []
        };
        const maxDataPoints = 100;

        // WebSocket bağlantısını başlat
        function connectWebSocket() {
            const wsUrl = 'ws://localhost:8888'; // WebSocket server URL
            const token = 'your-jwt-token-here'; // JWT token gerekli
            
            try {
                ws = new WebSocket(`${wsUrl}?token=${token}`);
                
                ws.onopen = function() {
                    isConnected = true;
                    updateConnectionStatus(true);
                    addLog('WebSocket bağlantısı kuruldu', 'info');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleIncomingData(data);
                    } catch (error) {
                        addLog(`Veri parse hatası: ${error.message}`, 'error');
                    }
                };
                
                ws.onclose = function() {
                    isConnected = false;
                    updateConnectionStatus(false);
                    addLog('WebSocket bağlantısı kesildi', 'warning');
                    
                    // 5 saniye sonra yeniden bağlanmaya çalış
                    setTimeout(connectWebSocket, 5000);
                };
                
                ws.onerror = function(error) {
                    addLog(`WebSocket hatası: ${error}`, 'error');
                };
                
            } catch (error) {
                addLog(`Bağlantı hatası: ${error.message}`, 'error');
                setTimeout(connectWebSocket, 5000);
            }
        }

        // Gelen veriyi işle
        function handleIncomingData(data) {
            if (data.type === 'new_earthquake' && data.payload) {
                updateDashboard(data.payload);
                addLog(`Yeni veri alındı: ${data.payload.device_id}`, 'info');
            }
        }

        // Dashboard'u güncelle
        function updateDashboard(sensorData) {
            // Cihaz bilgileri
            document.getElementById('deviceIdDisplay').textContent = sensorData.device_id || '-';
            document.getElementById('deviceId').textContent = `Cihaz: ${sensorData.device_id || 'Bilinmiyor'}`;
            document.getElementById('sampleCount').textContent = sensorData.sample_count || '-';
            document.getElementById('timestamp').textContent = formatTimestamp(sensorData.timestamp);
            document.getElementById('lastUpdate').textContent = `Son Güncelleme: ${moment().format('HH:mm:ss')}`;

            // İvme verileri
            const accel = sensorData.current_acceleration || {};
            document.getElementById('accelX').textContent = (accel.x || 0).toFixed(3);
            document.getElementById('accelY').textContent = (accel.y || 0).toFixed(3);
            document.getElementById('accelZ').textContent = (accel.z || 0).toFixed(3);

            // Analiz verileri
            const analysis = sensorData.analysis || {};
            document.getElementById('magnitude').textContent = (analysis.magnitude || 0).toFixed(3);
            document.getElementById('mmiScale').textContent = (analysis.mmi_scale || 0).toFixed(1);
            document.getElementById('richterMag').textContent = (analysis.richter_magnitude || 0).toFixed(1);

            // PGA verileri
            const pga = analysis.pga || {};
            document.getElementById('pgaValue').textContent = (pga.r_pga || 0).toFixed(3);
            document.getElementById('pgaX').textContent = (pga.x_pga || 0).toFixed(3);
            document.getElementById('pgaY').textContent = (pga.y_pga || 0).toFixed(3);
            document.getElementById('pgaZ').textContent = (pga.z_pga || 0).toFixed(3);
            document.getElementById('pgaR').textContent = (pga.r_pga || 0).toFixed(3);

            // Kalibrasyon verileri
            const calibration = sensorData.calibration || {};
            document.getElementById('calibX').textContent = (calibration.x_offset || 0).toFixed(6);
            document.getElementById('calibY').textContent = (calibration.y_offset || 0).toFixed(6);
            document.getElementById('calibZ').textContent = (calibration.z_offset || 0).toFixed(6);

            // Filtre yüzdeleri
            document.getElementById('filterX').textContent = `${(analysis.x_filter_percentage || 0).toFixed(1)}%`;
            document.getElementById('filterY').textContent = `${(analysis.y_filter_percentage || 0).toFixed(1)}%`;
            document.getElementById('filterZ').textContent = `${(analysis.z_filter_percentage || 0).toFixed(1)}%`;
            document.getElementById('filterAvg').textContent = `${(analysis.avg_filter_percentage || 0).toFixed(1)}%`;

            // Alarm durumu
            updateAlarmStatus(sensorData.alarms || {});

            // Grafik güncelle
            updateChart(accel);
        }

        // Alarm durumunu güncelle
        function updateAlarmStatus(alarms) {
            const statusElement = document.getElementById('alarmStatus');

            if (alarms.earthquake_alarm) {
                statusElement.textContent = '🚨 DEPREM ALARMI';
                statusElement.className = 'alarm-status alarm-earthquake';
                addLog('DEPREM ALARMI AKTIF!', 'error');
            } else if (alarms.seismic_alarm) {
                statusElement.textContent = '⚠️ SİSMİK AKTİVİTE';
                statusElement.className = 'alarm-status alarm-seismic';
                addLog('Sismik aktivite tespit edildi', 'warning');
            } else {
                statusElement.textContent = '✅ NORMAL';
                statusElement.className = 'alarm-status alarm-normal';
            }
        }

        // Bağlantı durumunu güncelle
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            if (connected) {
                statusElement.textContent = 'Bağlı';
                statusElement.className = 'connection-status connected';
            } else {
                statusElement.textContent = 'Bağlantı Kesildi';
                statusElement.className = 'connection-status disconnected';
            }
        }

        // Zaman damgasını formatla
        function formatTimestamp(timestamp) {
            if (!timestamp) return '-';
            return moment.unix(timestamp).format('DD/MM/YYYY HH:mm:ss');
        }

        // Log ekle
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type === 'error' ? 'log-error' : type === 'warning' ? 'log-warning' : ''}`;
            logEntry.textContent = `${moment().format('HH:mm:ss')} - ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // Maksimum 50 log tutmak için eski logları sil
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // Grafik güncelle
        function updateChart(accel) {
            const now = new Date();

            // Veri ekle
            accelerationData.x.push(accel.x || 0);
            accelerationData.y.push(accel.y || 0);
            accelerationData.z.push(accel.z || 0);
            accelerationData.timestamps.push(now);

            // Maksimum veri noktası sınırı
            if (accelerationData.x.length > maxDataPoints) {
                accelerationData.x.shift();
                accelerationData.y.shift();
                accelerationData.z.shift();
                accelerationData.timestamps.shift();
            }

            // Plotly grafiğini güncelle
            const update = {
                x: [accelerationData.timestamps, accelerationData.timestamps, accelerationData.timestamps],
                y: [accelerationData.x, accelerationData.y, accelerationData.z]
            };

            Plotly.redraw('accelerationChart', update);
        }

        // Grafik başlat
        function initializeChart() {
            const data = [
                {
                    x: [],
                    y: [],
                    type: 'scatter',
                    mode: 'lines',
                    name: 'X Ekseni',
                    line: { color: '#FF6B6B', width: 2 }
                },
                {
                    x: [],
                    y: [],
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Y Ekseni',
                    line: { color: '#4ECDC4', width: 2 }
                },
                {
                    x: [],
                    y: [],
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Z Ekseni',
                    line: { color: '#45B7D1', width: 2 }
                }
            ];

            const layout = {
                title: {
                    text: 'İvme Verileri (g)',
                    font: { color: '#ffffff' }
                },
                xaxis: {
                    title: 'Zaman',
                    color: '#ffffff',
                    gridcolor: 'rgba(255,255,255,0.2)'
                },
                yaxis: {
                    title: 'İvme (g)',
                    color: '#ffffff',
                    gridcolor: 'rgba(255,255,255,0.2)'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0.3)',
                font: { color: '#ffffff' },
                legend: {
                    font: { color: '#ffffff' }
                },
                margin: { t: 50, r: 50, b: 50, l: 50 }
            };

            const config = {
                responsive: true,
                displayModeBar: false
            };

            Plotly.newPlot('accelerationChart', data, layout, config);
        }

        // Sayfa yüklendiğinde başlat
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Sistem başlatılıyor...', 'info');
            initializeChart();
            connectWebSocket();

            // Test verisi için (gerçek bağlantı yoksa)
            if (!isConnected) {
                setTimeout(() => {
                    if (!isConnected) {
                        addLog('WebSocket bağlantısı kurulamadı, test verisi kullanılıyor', 'warning');
                        startTestData();
                    }
                }, 3000);
            }
        });

        // Test verisi (bağlantı yoksa)
        function startTestData() {
            setInterval(() => {
                const testData = {
                    device_id: 'pi_001_test',
                    current_acceleration: {
                        x: (Math.random() - 0.5) * 0.1,
                        y: (Math.random() - 0.5) * 0.1,
                        z: 9.81 + (Math.random() - 0.5) * 0.2
                    },
                    analysis: {
                        magnitude: Math.random() * 0.05,
                        mmi_scale: Math.random() * 3,
                        richter_magnitude: Math.random() * 2,
                        pga: {
                            x_pga: Math.random() * 0.01,
                            y_pga: Math.random() * 0.01,
                            z_pga: Math.random() * 0.01,
                            r_pga: Math.random() * 0.02
                        },
                        x_filter_percentage: 85 + Math.random() * 10,
                        y_filter_percentage: 85 + Math.random() * 10,
                        z_filter_percentage: 85 + Math.random() * 10,
                        avg_filter_percentage: 85 + Math.random() * 10
                    },
                    alarms: {
                        earthquake_alarm: false,
                        seismic_alarm: Math.random() > 0.9
                    },
                    calibration: {
                        x_offset: (Math.random() - 0.5) * 0.001,
                        y_offset: (Math.random() - 0.5) * 0.001,
                        z_offset: (Math.random() - 0.5) * 0.001
                    },
                    sample_count: Math.floor(Math.random() * 1000) + 1000,
                    timestamp: Math.floor(Date.now() / 1000)
                };

                updateDashboard(testData);
            }, 1000);
        }
    </script>
</body>
</html>
