# 🌐 Earthquake WebSocket Bridge - Deployment Guide

## 📋 What is earthquake-websocket?

The **earthquake-websocket** service is a **real-time data bridge** that connects your Kafka message broker to web dashboards and clients.

### 🔄 Data Flow:
```
Raspberry Pi → Kafka → earthquake-websocket → Dashboard/Web Clients
                            ↓
                       Laravel Admin Panel
```

### 🎯 Key Functions:
- **Kafka Consumer**: Listens to earthquake data from Raspberry Pi devices
- **WebSocket Server**: Provides real-time data to dashboards (port 8888)
- **HTTP API**: REST endpoints for status and alerts (port 3001)
- **MCP Alert System**: Emergency alerts to specific devices
- **JWT Authentication**: Secure WebSocket connections

## 🏗️ Where to Deploy

### ✅ **RECOMMENDED: Hetzner Server (Same as <PERSON><PERSON>)**

**Benefits:**
- Centralized architecture
- Better network performance
- Easier management
- Cost effective
- Internal network security

### ❌ **NOT RECOMMENDED: Raspberry Pi**

**Why not on Pi:**
- Limited resources
- Network dependency
- Single point of failure
- Harder to scale

## 🚀 Quick Deployment

### 1. **Automated Deployment Script**

```bash
# Make deployment script executable
chmod +x earthquake-websocket/deploy-to-hetzner.sh

# Edit the script with your server details
nano earthquake-websocket/deploy-to-hetzner.sh
# Update: HETZNER_SERVER_IP="YOUR_SERVER_IP"

# Run deployment
./earthquake-websocket/deploy-to-hetzner.sh
```

### 2. **Manual Deployment Steps**

```bash
# 1. Copy files to server
scp -r earthquake-websocket/ root@YOUR_SERVER:/opt/

# 2. SSH into server
ssh root@YOUR_SERVER

# 3. Install dependencies
cd /opt/earthquake-websocket
npm install

# 4. Create environment file
cat > .env << 'EOF'
NODE_ENV=production
KAFKA_BROKERS=kafka.niny.tech:9093
KAFKA_SECURITY_PROTOCOL=SASL_PLAINTEXT
KAFKA_SASL_MECHANISM=SCRAM-SHA-512
KAFKA_SASL_USERNAME=user
KAFKA_SASL_PASSWORD=Fatih123
KAFKA_TOPICS=pi_001,balikesir_pi_001,pi_007,pi_008,pi_009,pi_010,pi_011
WS_PORT=8888
HTTP_PORT=3001
TOKEN=31mif5cvZUp/RPNTIOlAz+/wqvjg6spsSIfnsoAj7DI=
EOF

# 5. Start service
node src/index.js
```

### 3. **Docker Deployment (Alternative)**

```bash
# On Hetzner server
cd /opt/earthquake-websocket
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs -f
```

## 🔧 Configuration

### **Environment Variables**

| Variable | Description | Example |
|----------|-------------|---------|
| `KAFKA_BROKERS` | Kafka server address | `kafka.niny.tech:9093` |
| `KAFKA_TOPICS` | Topics to listen to | `pi_001,balikesir_pi_001` |
| `WS_PORT` | WebSocket server port | `8888` |
| `HTTP_PORT` | HTTP API port | `3001` |
| `TOKEN` | JWT secret for authentication | `your-secure-token` |

### **Adding New Raspberry Pi Devices**

When you add a new Raspberry Pi device, update the topics:

```bash
# Edit environment
nano /opt/earthquake-websocket/.env

# Add new device topic
KAFKA_TOPICS=pi_001,balikesir_pi_001,NEW_DEVICE_ID

# Restart service
systemctl restart earthquake-websocket
```

## 📊 Monitoring & Status

### **Check Service Status**

```bash
# Service status
systemctl status earthquake-websocket

# View logs
journalctl -u earthquake-websocket -f

# Check ports
netstat -tlnp | grep -E ':(8888|3001)'
```

### **API Endpoints**

```bash
# WebSocket status
curl http://YOUR_SERVER:3001/api/status

# Expected response:
{
  "websocket": {
    "active_connections": 2,
    "active_channels": ["pi_001", "balikesir_pi_001"]
  },
  "uptime": 3600
}
```

### **Test WebSocket Connection**

```javascript
// Test from browser console
const ws = new WebSocket('ws://YOUR_SERVER:8888?token=YOUR_TOKEN');
ws.onopen = () => console.log('Connected');
ws.onmessage = (event) => console.log('Data:', JSON.parse(event.data));
```

## 🔒 Security

### **Firewall Configuration**

```bash
# Allow WebSocket port
ufw allow 8888/tcp

# Allow HTTP API port
ufw allow 3001/tcp

# Check firewall status
ufw status
```

### **JWT Token Security**

```bash
# Generate secure token
openssl rand -base64 32

# Update in .env file
TOKEN=your-new-secure-token
```

## 🐛 Troubleshooting

### **Common Issues**

1. **Kafka Connection Failed**
   ```bash
   # Check Kafka connectivity
   telnet kafka.niny.tech 9093
   
   # Verify credentials
   grep -E "KAFKA_SASL_" /opt/earthquake-websocket/.env
   ```

2. **WebSocket Connection Refused**
   ```bash
   # Check if service is running
   systemctl status earthquake-websocket
   
   # Check port binding
   netstat -tlnp | grep 8888
   ```

3. **No Data Received**
   ```bash
   # Check Kafka topics
   # Verify Raspberry Pi is sending data
   # Check service logs
   journalctl -u earthquake-websocket -f
   ```

### **Debug Mode**

```bash
# Enable debug logging
export DEBUG=*
node src/index.js

# Or edit .env
NODE_ENV=development
```

## 🔄 Integration with Dashboard

### **Update Dashboard Configuration**

After deploying earthquake-websocket, update your dashboard:

```javascript
// In dashboard-config.js
DASHBOARD_CONFIG.connection.websocketUrl = 'ws://YOUR_HETZNER_IP:8888';
DASHBOARD_CONFIG.connection.jwtToken = 'your-jwt-token-from-laravel';
```

### **Laravel Integration**

The WebSocket service integrates with Laravel for:
- Device management
- Alert notifications
- User authentication
- Historical data

## 📈 Performance

### **Resource Usage**
- **Memory**: ~50-100MB
- **CPU**: <5% under normal load
- **Network**: Depends on number of devices and clients

### **Scaling**
- Can handle 100+ concurrent WebSocket connections
- Kafka consumer can process high-throughput data
- Horizontal scaling possible with load balancer

## 🎯 Next Steps

1. **Deploy earthquake-websocket to Hetzner server**
2. **Update Raspberry Pi configuration** to include your device topic
3. **Configure dashboard** to connect to WebSocket server
4. **Test end-to-end data flow**
5. **Monitor service health**

---

**🌍 Your earthquake monitoring system architecture:**
```
Raspberry Pi (Balıkesir) → Kafka (Hetzner) → WebSocket Bridge (Hetzner) → Dashboard (Pi/Web)
                                                      ↓
                                              Laravel Admin (Hetzner)
```
