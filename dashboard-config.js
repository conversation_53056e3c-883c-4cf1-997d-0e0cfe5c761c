// 🌍 Gelişmiş Deprem İzleme Sistemi - Dashboard Konfigürasyon
// Bu dosyayı dashboard'unuzu özelleştirmek için kullanın

const DASHBOARD_CONFIG = {
    // 🔗 Bağlantı Ayarları
    connection: {
        // WebSocket server URL'i (earthquake-websocket projesi)
        websocketUrl: 'ws://localhost:8888',
        
        // HTTP API URL'i (durum kontrolü için)
        apiUrl: 'http://localhost:3001',
        
        // JWT token (Laravel'den alınacak)
        // Gerçek kullanımda bu değer dinamik olarak set edilmelidir
        jwtToken: 'your-jwt-token-here',
        
        // Yeniden bağlantı ayarları
        reconnectInterval: 5000, // 5 saniye
        maxReconnectAttempts: 10,
        
        // Bağlantı timeout süresi
        connectionTimeout: 10000 // 10 saniye
    },

    // 📊 Grafik Ayarları
    chart: {
        // Maksimum veri noktası sayısı (performans için)
        maxDataPoints: 100,
        
        // Grafik güncelleme sıklığı (ms)
        updateInterval: 1000,
        
        // Grafik renkleri
        colors: {
            xAxis: '#FF6B6B',  // Kırmızı
            yAxis: '#4ECDC4',  // Turkuaz
            zAxis: '#45B7D1'   // Mavi
        },
        
        // Grafik boyutları
        height: 400,
        
        // Animasyon ayarları
        animation: {
            enabled: true,
            duration: 300
        }
    },

    // 🚨 Alarm Ayarları
    alarms: {
        // Alarm eşikleri (bu değerler Raspberry Pi'den gelir ama UI için referans)
        thresholds: {
            seismic: {
                mmi: 4.0,
                richter: 1.0
            },
            earthquake: {
                mmi: 6.0,
                richter: 5.0
            }
        },
        
        // Alarm efektleri
        effects: {
            // Sismik alarm için yanıp sönme hızı (ms)
            blinkInterval: 1000,
            
            // Deprem alarmı için titreşim efekti
            shakeEnabled: true,
            
            // Ses uyarısı (tarayıcı izni gerekli)
            soundEnabled: false,
            soundFile: 'alarm.mp3'
        },
        
        // Alarm renkleri
        colors: {
            normal: '#4CAF50',    // Yeşil
            seismic: '#FF9800',   // Turuncu
            earthquake: '#f44336' // Kırmızı
        }
    },

    // 🎨 UI Ayarları
    ui: {
        // Tema ayarları
        theme: {
            // Ana renk paleti
            primaryColor: '#4CAF50',
            warningColor: '#FF9800',
            dangerColor: '#f44336',
            
            // Arka plan gradyanı
            backgroundGradient: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
            
            // Panel şeffaflığı
            panelOpacity: 0.1,
            
            // Yazı tipi
            fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
        },
        
        // Responsive breakpoint'ler
        breakpoints: {
            mobile: 768,
            tablet: 1024,
            desktop: 1200
        },
        
        // Animasyon ayarları
        animations: {
            enabled: true,
            duration: 300,
            easing: 'ease-in-out'
        }
    },

    // 📝 Log Ayarları
    logging: {
        // Maksimum log sayısı
        maxLogEntries: 50,
        
        // Log seviyeleri
        levels: {
            info: true,
            warning: true,
            error: true,
            debug: false // Production'da false olmalı
        },
        
        // Log formatı
        timestampFormat: 'HH:mm:ss',
        
        // Otomatik scroll
        autoScroll: true
    },

    // 🔢 Veri Formatı Ayarları
    dataFormat: {
        // Ondalık basamak sayıları
        decimals: {
            acceleration: 3,
            magnitude: 3,
            mmi: 1,
            richter: 1,
            pga: 3,
            calibration: 6,
            percentage: 1
        },
        
        // Zaman formatı
        timestampFormat: 'DD/MM/YYYY HH:mm:ss',
        
        // Birimler
        units: {
            acceleration: 'g',
            magnitude: 'g',
            pga: 'g',
            percentage: '%'
        }
    },

    // 🧪 Test Modu Ayarları
    testMode: {
        // Test modu etkin mi?
        enabled: false,
        
        // Test verisi güncelleme sıklığı (ms)
        updateInterval: 1000,
        
        // Test verisi aralıkları
        ranges: {
            acceleration: {
                x: [-0.1, 0.1],
                y: [-0.1, 0.1],
                z: [9.7, 9.9]
            },
            magnitude: [0, 0.05],
            mmi: [0, 3],
            richter: [0, 2]
        },
        
        // Rastgele alarm tetikleme olasılığı
        alarmProbability: {
            seismic: 0.1,  // %10
            earthquake: 0.01 // %1
        }
    },

    // 📱 Mobil Ayarları
    mobile: {
        // Mobil cihazlarda grafik boyutu
        chartHeight: 250,
        
        // Touch gesture'ları
        touchEnabled: true,
        
        // Mobil optimizasyonları
        optimizations: {
            // Düşük performanslı cihazlarda veri noktası sayısını azalt
            reduceDataPoints: true,
            maxDataPointsMobile: 50,
            
            // Animasyonları devre dışı bırak
            disableAnimations: false
        }
    },

    // 🔧 Gelişmiş Ayarlar
    advanced: {
        // WebSocket mesaj buffer boyutu
        messageBufferSize: 100,
        
        // Veri validasyon
        dataValidation: {
            enabled: true,
            strictMode: false
        },
        
        // Performans izleme
        performance: {
            enabled: false,
            logInterval: 30000 // 30 saniye
        },
        
        // Debug modu
        debug: {
            enabled: false,
            logWebSocketMessages: false,
            logDataUpdates: false
        }
    },

    // 🌐 Çoklu Dil Desteği
    localization: {
        // Varsayılan dil
        defaultLanguage: 'tr',
        
        // Desteklenen diller
        supportedLanguages: ['tr', 'en'],
        
        // Dil dosyaları
        translations: {
            tr: {
                title: 'Gelişmiş Deprem İzleme Sistemi',
                deviceStatus: 'Cihaz Durumu',
                accelerationData: 'İvme Verileri',
                seismicAnalysis: 'Sismik Analiz',
                alarmStatus: {
                    normal: '✅ NORMAL',
                    seismic: '⚠️ SİSMİK AKTİVİTE',
                    earthquake: '🚨 DEPREM ALARMI'
                }
            },
            en: {
                title: 'Advanced Earthquake Monitoring System',
                deviceStatus: 'Device Status',
                accelerationData: 'Acceleration Data',
                seismicAnalysis: 'Seismic Analysis',
                alarmStatus: {
                    normal: '✅ NORMAL',
                    seismic: '⚠️ SEISMIC ACTIVITY',
                    earthquake: '🚨 EARTHQUAKE ALARM'
                }
            }
        }
    }
};

// 🔧 Konfigürasyon yardımcı fonksiyonları
const ConfigHelper = {
    // Konfigürasyonu localStorage'a kaydet
    save: function() {
        localStorage.setItem('earthquakeDashboardConfig', JSON.stringify(DASHBOARD_CONFIG));
    },
    
    // Konfigürasyonu localStorage'dan yükle
    load: function() {
        const saved = localStorage.getItem('earthquakeDashboardConfig');
        if (saved) {
            try {
                const config = JSON.parse(saved);
                Object.assign(DASHBOARD_CONFIG, config);
            } catch (error) {
                console.warn('Kaydedilmiş konfigürasyon yüklenemedi:', error);
            }
        }
    },
    
    // Konfigürasyonu sıfırla
    reset: function() {
        localStorage.removeItem('earthquakeDashboardConfig');
        location.reload();
    },
    
    // Belirli bir ayarı güncelle
    update: function(path, value) {
        const keys = path.split('.');
        let current = DASHBOARD_CONFIG;
        
        for (let i = 0; i < keys.length - 1; i++) {
            current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
        this.save();
    },
    
    // Belirli bir ayarı al
    get: function(path) {
        const keys = path.split('.');
        let current = DASHBOARD_CONFIG;
        
        for (const key of keys) {
            current = current[key];
            if (current === undefined) return undefined;
        }
        
        return current;
    }
};

// Sayfa yüklendiğinde konfigürasyonu yükle
if (typeof window !== 'undefined') {
    ConfigHelper.load();
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DASHBOARD_CONFIG, ConfigHelper };
}
