# 🌍 Gelişmiş Deprem İzleme Sistemi - Dashboard

Bu proje, eski deprem izleme sisteminin yerine geçen modern, gelişmiş bir dashboard'dur. <PERSON><PERSON>, Raspberry Pi tabanlı ADXL355 sensörlerinden gelen verileri gerçek zamanlı olarak görüntüler ve analiz eder.

## 📋 <PERSON><PERSON>a <PERSON>

```
├── index.html                    # Ana dashboard dosyası
├── new-earthquake-dashboard.html  # Basit versiyon (konfigürasyon olmadan)
├── dashboard-config.js           # Konfigürasyon dosyası
├── dashboard-setup-guide.md      # Detaylı kurulum rehberi
└── README.md                     # Bu dosya
```

## 🚀 Hızlı Başlangıç

### 1. Dosyaları İndirin
Tüm dosyaları aynı klasöre yerleştirin.

### 2. WebSocket Server'ı Başlatın
```bash
cd earthquake-websocket
npm install
npm start
```

### 3. Dashboard'u Açın
`index.html` dosyasını web tarayıcınızda açın.

### 4. Ayarları Yapılandırın
- Sağ üst köşedeki ⚙️ butonuna tıklayın
- WebSocket URL'ini ayarlayın (varsayılan: `ws://localhost:8888`)
- JWT token'ınızı girin (Laravel'den alınacak)
- Diğer ayarları isteğinize göre düzenleyin

## 🔧 Konfigürasyon

### WebSocket Bağlantısı
```javascript
// dashboard-config.js içinde
connection: {
    websocketUrl: 'ws://localhost:8888',
    jwtToken: 'your-jwt-token-here'
}
```

### JWT Token Alma
Laravel uygulamanızdan token alın:
```php
$payload = ['id' => $user->id, 'iat' => now()->timestamp, 'exp' => now()->addHours(1)->timestamp];
$token = JWT::encode($payload, config('jwt.jwt_secret'), 'HS256');
```

## 📊 Özellikler

### 🆚 Eski vs Yeni Sistem

| Özellik | Eski Sistem | Yeni Sistem |
|---------|-------------|-------------|
| **Veri Kaynağı** | Socket.IO | WebSocket |
| **Sensör** | Basit ivmeölçer | ADXL355 (gelişmiş) |
| **Analiz** | Basit eşik kontrolü | Richter, MMI, PGA |
| **Alarm** | 2 seviye | 3 seviye (Normal/Seismic/Earthquake) |
| **Kalibrasyon** | Manuel | Otomatik |
| **UI** | Statik HTML | Modern, responsive |

### 📈 Dashboard Bölümleri

#### Sol Panel - Cihaz Durumu
- **Cihaz Bilgileri**: ID, örnek sayısı, zaman
- **Alarm Durumu**: Görsel alarm göstergesi
- **İvme Verileri**: X, Y, Z eksenleri (g cinsinden)
- **Kalibrasyon**: Otomatik offset değerleri

#### Orta Panel - Grafikler
- **Gerçek Zamanlı Grafik**: 3 eksen ivme grafiği
- **Sismik Metrikler**: 
  - Büyüklük (g)
  - MMI Ölçeği
  - Richter Büyüklüğü
  - PGA (Peak Ground Acceleration)

#### Sağ Panel - Detaylı Analiz
- **PGA Değerleri**: X, Y, Z, R eksenleri
- **Filtre Yüzdeleri**: Sinyal kalitesi
- **Sistem Logları**: Gerçek zamanlı mesajlar

## 🚨 Alarm Sistemi

### Alarm Seviyeleri

1. **✅ NORMAL** (Yeşil)
   - MMI < 4.0
   - Normal operasyon

2. **⚠️ SİSMİK AKTİVİTE** (Turuncu, yanıp söner)
   - MMI ≥ 4.0 ve < 6.0
   - Sismik aktivite tespit edildi

3. **🚨 DEPREM ALARMI** (Kırmızı, titreşim efekti)
   - MMI ≥ 6.0
   - Kritik deprem durumu

## 🧪 Test Modu

Gerçek veri yoksa dashboard otomatik test moduna geçer:

```javascript
// Test modu manuel olarak etkinleştirme
DASHBOARD_CONFIG.testMode.enabled = true;
```

Test modu özellikleri:
- Simüle edilmiş deprem verileri
- Rastgele alarm tetikleme
- Gerçekçi veri aralıkları

## 📱 Responsive Tasarım

Dashboard farklı ekran boyutlarına uyum sağlar:
- **Desktop**: 3 sütunlu layout
- **Tablet**: 2 sütunlu layout  
- **Mobil**: Tek sütunlu layout

## 🔧 Özelleştirme

### Renk Teması
```css
:root {
  --primary-color: #4CAF50;
  --warning-color: #FF9800;
  --danger-color: #f44336;
}
```

### Grafik Ayarları
```javascript
chart: {
    maxDataPoints: 100,
    updateInterval: 1000,
    colors: {
        xAxis: '#FF6B6B',
        yAxis: '#4ECDC4', 
        zAxis: '#45B7D1'
    }
}
```

## 🐛 Sorun Giderme

### Bağlantı Sorunları

1. **WebSocket bağlantısı kurulamıyor**
   - WebSocket server'ın çalıştığını kontrol edin
   - Port 8888'in açık olduğunu doğrulayın
   - JWT token'ın geçerli olduğunu kontrol edin

2. **Veri gelmiyor**
   - Raspberry Pi client'ın çalıştığını kontrol edin
   - Kafka bağlantısını doğrulayın
   - Test modunu deneyin

3. **Grafik görünmüyor**
   - Plotly kütüphanesinin yüklendiğini kontrol edin
   - Tarayıcı konsolunda hata mesajlarını inceleyin

### Debug Modu

Debug modunu etkinleştirmek için:
```javascript
DASHBOARD_CONFIG.advanced.debug.enabled = true;
```

## 📊 Veri Formatı

Dashboard şu formatta veri bekler:

```json
{
  "type": "new_earthquake",
  "payload": {
    "device_id": "pi_001",
    "current_acceleration": {
      "x": 0.123, "y": -0.045, "z": 9.876
    },
    "analysis": {
      "magnitude": 0.234,
      "mmi_scale": 2.1,
      "richter_magnitude": 1.8,
      "pga": {
        "x_pga": 0.001, "y_pga": 0.002,
        "z_pga": 0.003, "r_pga": 0.004
      }
    },
    "alarms": {
      "earthquake_alarm": false,
      "seismic_alarm": true
    },
    "calibration": {
      "x_offset": 0.001,
      "y_offset": -0.002,
      "z_offset": 0.000
    },
    "sample_count": 1234,
    "timestamp": 1705311045
  }
}
```

## 🔒 Güvenlik

- **JWT Authentication**: Tüm WebSocket bağlantıları token ile korunur
- **CORS**: Cross-origin istekleri kontrol edilir
- **Input Validation**: Gelen veriler doğrulanır

## 📈 Performans

- **Veri Sıkıştırma**: JSON verileri optimize edilir
- **Bellek Yönetimi**: Eski veriler otomatik temizlenir
- **Responsive**: Mobil cihazlarda optimize edilir

## 🔄 Otomatik Özellikler

- **Yeniden Bağlantı**: Bağlantı koptuğunda otomatik yeniden bağlanır
- **Test Modu**: Bağlantı yoksa otomatik test verisi
- **Ayar Kaydetme**: Ayarlar localStorage'da saklanır
- **Graceful Degradation**: Hata durumlarında düzgün çalışmaya devam eder

## 📞 Destek

Sorunlar için:
1. Tarayıcı geliştirici araçlarını kontrol edin
2. WebSocket server loglarını inceleyin
3. Test modunu deneyin
4. `dashboard-setup-guide.md` dosyasını okuyun

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

---

**Not**: Bu dashboard, eski sistem yerine geçmek üzere tasarlanmıştır. Gelişmiş analiz özellikleri ve modern UI ile daha iyi bir kullanıcı deneyimi sunar.
