#!/bin/bash
# Real-time earthquake monitoring display script for Raspberry Pi
# This script should be deployed to /home/<USER>/rasberrypi-python-client/

LOG_FILE="/var/log/adxl355.log"
CONFIG_FILE="/home/<USER>/rasberrypi-python-client/config.json"

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Function to get latest sensor data
get_latest_data() {
    # Try to get the most recent earthquake data from log
    if [ -f "$LOG_FILE" ]; then
        tail -n 1 "$LOG_FILE" 2>/dev/null || echo "{}"
    else
        echo "{}"
    fi
}

# Function to get system status
get_system_status() {
    local cpu_temp=$(vcgencmd measure_temp 2>/dev/null | cut -d'=' -f2 | cut -d"'" -f1 || echo "N/A")
    local memory_usage=$(free | awk 'FNR==2{printf "%.1f", ($3/$2)*100}')
    local disk_usage=$(df /home | awk 'NR==2 {print $5}' | sed 's/%//')
    
    echo "$cpu_temp,$memory_usage,$disk_usage"
}

# Function to parse earthquake magnitude level
get_magnitude_level() {
    local magnitude=$1
    if (( $(echo "$magnitude < 2.0" | bc -l) )); then
        echo "Micro"
    elif (( $(echo "$magnitude < 3.0" | bc -l) )); then
        echo "Minor"
    elif (( $(echo "$magnitude < 4.0" | bc -l) )); then
        echo "Light"
    elif (( $(echo "$magnitude < 5.0" | bc -l) )); then
        echo "Moderate"
    elif (( $(echo "$magnitude < 6.0" | bc -l) )); then
        echo "Strong"
    elif (( $(echo "$magnitude < 7.0" | bc -l) )); then
        echo "Major"
    else
        echo "Great"
    fi
}

# Function to display header
display_header() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║${WHITE}                        EARTHQUAKE MONITORING SYSTEM                         ${BLUE}║${NC}"
    echo -e "${BLUE}║${WHITE}                           Real-Time Device Monitor                          ${BLUE}║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Function to display system info
display_system_info() {
    local device_name=$(hostname)
    local current_time=$(date '+%Y-%m-%d %H:%M:%S')
    local system_status=$(get_system_status)
    local cpu_temp=$(echo $system_status | cut -d',' -f1)
    local memory_usage=$(echo $system_status | cut -d',' -f2)
    local disk_usage=$(echo $system_status | cut -d',' -f3)
    
    echo -e "${CYAN}┌─ SYSTEM STATUS ─────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${CYAN}│${NC} Device: ${WHITE}$device_name${NC}"
    echo -e "${CYAN}│${NC} Time: ${WHITE}$current_time${NC}"
    echo -e "${CYAN}│${NC} CPU Temp: ${WHITE}$cpu_temp°C${NC} | Memory: ${WHITE}$memory_usage%${NC} | Disk: ${WHITE}$disk_usage%${NC}"
    echo -e "${CYAN}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    echo ""
}

# Function to display earthquake data
display_earthquake_data() {
    local data=$(get_latest_data)
    
    # Parse JSON data (simplified - in real implementation you'd use jq)
    local timestamp=$(echo "$data" | grep -o '"timestamp":[^,]*' | cut -d':' -f2 | tr -d '"' 2>/dev/null || echo "N/A")
    local magnitude=$(echo "$data" | grep -o '"richter_magnitude":[^,}]*' | cut -d':' -f2 | tr -d '"' 2>/dev/null || echo "0.0")
    local acc_x=$(echo "$data" | grep -o '"x":[^,}]*' | head -1 | cut -d':' -f2 | tr -d '"' 2>/dev/null || echo "0.0")
    local acc_y=$(echo "$data" | grep -o '"y":[^,}]*' | head -1 | cut -d':' -f2 | tr -d '"' 2>/dev/null || echo "0.0")
    local acc_z=$(echo "$data" | grep -o '"z":[^,}]*' | head -1 | cut -d':' -f2 | tr -d '"' 2>/dev/null || echo "0.0")
    
    # Get magnitude level and color
    local mag_level=$(get_magnitude_level $magnitude)
    local mag_color=$GREEN
    if (( $(echo "$magnitude >= 4.0" | bc -l) )); then
        mag_color=$YELLOW
    fi
    if (( $(echo "$magnitude >= 6.0" | bc -l) )); then
        mag_color=$RED
    fi
    
    echo -e "${PURPLE}┌─ SEISMIC DATA ──────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${PURPLE}│${NC} Last Update: ${WHITE}$timestamp${NC}"
    echo -e "${PURPLE}│${NC}"
    echo -e "${PURPLE}│${NC} ${WHITE}ACCELERATION (g):${NC}"
    echo -e "${PURPLE}│${NC}   X-axis: ${CYAN}$acc_x${NC}"
    echo -e "${PURPLE}│${NC}   Y-axis: ${CYAN}$acc_y${NC}"
    echo -e "${PURPLE}│${NC}   Z-axis: ${CYAN}$acc_z${NC}"
    echo -e "${PURPLE}│${NC}"
    echo -e "${PURPLE}│${NC} ${WHITE}EARTHQUAKE ANALYSIS:${NC}"
    echo -e "${PURPLE}│${NC}   Magnitude: ${mag_color}$magnitude${NC}"
    echo -e "${PURPLE}│${NC}   Level: ${mag_color}$mag_level${NC}"
    echo -e "${PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    echo ""
}

# Function to display alarm status
display_alarm_status() {
    local data=$(get_latest_data)
    local earthquake_alarm=$(echo "$data" | grep -o '"earthquake_alarm":[^,}]*' | cut -d':' -f2 | tr -d '"' 2>/dev/null || echo "false")
    local seismic_alarm=$(echo "$data" | grep -o '"seismic_alarm":[^,}]*' | cut -d':' -f2 | tr -d '"' 2>/dev/null || echo "false")
    
    local status_color=$GREEN
    local status_text="NORMAL"
    
    if [ "$earthquake_alarm" = "true" ]; then
        status_color=$RED
        status_text="🚨 EARTHQUAKE ALARM 🚨"
    elif [ "$seismic_alarm" = "true" ]; then
        status_color=$YELLOW
        status_text="⚠️  SEISMIC ACTIVITY ⚠️"
    fi
    
    echo -e "${RED}┌─ ALARM STATUS ──────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${RED}│${NC} Status: ${status_color}$status_text${NC}"
    echo -e "${RED}│${NC} Earthquake Alarm: $([ "$earthquake_alarm" = "true" ] && echo -e "${RED}ACTIVE${NC}" || echo -e "${GREEN}INACTIVE${NC}")"
    echo -e "${RED}│${NC} Seismic Alarm: $([ "$seismic_alarm" = "true" ] && echo -e "${YELLOW}ACTIVE${NC}" || echo -e "${GREEN}INACTIVE${NC}")"
    echo -e "${RED}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    echo ""
}

# Function to display footer
display_footer() {
    echo -e "${BLUE}┌─ CONTROLS ──────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BLUE}│${NC} Press ${WHITE}Ctrl+C${NC} to exit monitoring"
    echo -e "${BLUE}│${NC} Refresh rate: ${WHITE}1 second${NC}"
    echo -e "${BLUE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
}

# Main monitoring loop
main() {
    echo "Starting real-time earthquake monitoring..."
    echo "Press Ctrl+C to stop"
    sleep 2
    
    # Trap Ctrl+C to exit gracefully
    trap 'echo -e "\n${WHITE}Monitoring stopped.${NC}"; exit 0' INT
    
    while true; do
        display_header
        display_system_info
        display_earthquake_data
        display_alarm_status
        display_footer
        
        sleep 1
    done
}

# Check if script is run directly
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
